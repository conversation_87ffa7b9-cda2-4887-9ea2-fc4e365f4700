import { BadRequestException, Injectable } from '@nestjs/common';
import { BlockchainService } from '@root/blockchain/blockchain.service';
import { KeyValueGroup } from '@root/key-value-group/key-value-group.entity';
import { AppType, TaskType } from '@root/task/task.constants';
import { Task } from '@root/task/task.entity';
import { TaskService } from '@root/task/task.service';
import { IpApiService } from '@root/utils/ip-api.service';
import { RecaptchaService } from '@root/utils/recaptcha.service';
import { DataSource } from 'typeorm';
import {
  FaucetRawTaskData,
  FaucetRawTaskDataUpdateInput,
  FaucetRawTaskInput,
  FaucetRawTaskUpdateInput,
} from './faucet-task.dto';

@Injectable()
export class FaucetService {
  constructor(
    private dataSource: DataSource,
    private taskService: TaskService,
    private blockchainService: BlockchainService,
    private readonly ipApiService: IpApiService,
    private readonly recaptchaService: RecaptchaService,
  ) {}

  async create(
    projectId: string,
    eventId: string,
    data: FaucetRawTaskInput,
    taskType: TaskType.FAUCET_EVM | TaskType.FAUCET_DOTSAMA,
  ): Promise<string> {
    await this.validateTaskData(eventId, data);

    return this.dataSource.transaction(async (transactionalEntityManager) => {
      const taskId = await this.taskService.create<FaucetRawTaskData>(
        projectId,
        eventId,
        data,
        AppType.FAUCET,
        taskType,
        transactionalEntityManager,
      );
      if (data?.data?.allocationCSVExists && data?.allocationCSV) {
        await transactionalEntityManager
          .createQueryBuilder()
          .insert()
          .into(KeyValueGroup)
          .values(
            data?.allocationCSV?.map((i) => ({
              ...i,
              key: i.key.toLowerCase(),
              groupId: taskId,
            })),
          )
          .execute();
      }
      return taskId;
    });
  }

  async update(
    projectId: string,
    eventId: string,
    taskId: string,
    taskDto: FaucetRawTaskUpdateInput,
  ): Promise<number> {
    await this.validateTaskData(eventId, taskDto);

    return this.dataSource.transaction(async (transactionalEntityManager) => {
      const kv = taskDto?.allocationCSV ? [...taskDto?.allocationCSV] : [];
      delete taskDto?.allocationCSV;
      const affected =
        await this.taskService.update<FaucetRawTaskDataUpdateInput>(
          projectId,
          eventId,
          taskId,
          taskDto,
          false,
          transactionalEntityManager,
        );
      await transactionalEntityManager
        .createQueryBuilder()
        .delete()
        .from(KeyValueGroup)
        .where({
          groupId: taskId,
        })
        .execute();
      if (kv && kv.length > 0) {
        await transactionalEntityManager
          .createQueryBuilder()
          .insert()
          .into(KeyValueGroup)
          .values(
            kv?.map((i) => ({
              ...i,
              key: i.key.toLowerCase(),
              groupId: taskId,
            })),
          )
          .execute();
      }
      return affected;
    });
  }

  private async validateTaskData(
    eventId: string,
    data: FaucetRawTaskUpdateInput | FaucetRawTaskInput,
  ) {
    if (!data.data.allocationCSVExists) {
      if (!data.data.amountPerUser) {
        throw new BadRequestException(
          'Please specify a fixed amount per user or select the correct allocation type',
        );
      }
      // const connectedAppTypes: AppType[] | undefined =
      //   await this.taskService.findConnectedAppTypesByEventId(eventId);

      // const isRequiredAppConnected = [
      //   AppType.DISCORD,
      //   AppType.EMAIL,
      //   AppType.TWITTER,
      // ].every((appType) => connectedAppTypes?.includes(appType));

      // if (!isRequiredAppConnected) {
      //   throw new BadRequestException(
      //     'Please include all required tasks before creating a faucet [DISCORD, EMAIL, TWITTER]. This will help prevent double claims.',
      //   );
      // }
    }

    if (data.data.allocationCSVExists && !data.allocationCSV) {
      throw new BadRequestException(
        'Please upload an allocation CSV or select the correct allocation type',
      );
    }

    const blockchain = await this.blockchainService.findById(
      data.data.blockchainId,
    );

    if (!blockchain || !blockchain.rpcUrls.length) {
      throw new BadRequestException(
        'The requested blockchain is not supported.',
      );
    }
  }

  async validateParticipation(
    task: Task<FaucetRawTaskData>,
    address: string,
    ip: string,
    captcha: string,
  ): Promise<boolean> {
    const vpnIp = await this.ipApiService.isVpn(ip);
    if (vpnIp)
      throw new BadRequestException('Reward claim via VPN is not allowed');
    const verified = await this.recaptchaService.verify(captcha);
    if (!verified) throw new BadRequestException('Captcha verification failed');
    return true;
  }
}
