"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/user/[user]/campaigns",{

/***/ "./components/Giveaways/components/GiveawaySummaryTags.tsx":
/*!*****************************************************************!*\
  !*** ./components/Giveaways/components/GiveawaySummaryTags.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GiveawaySummaryTags: function() { return /* binding */ GiveawaySummaryTags; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/FeatureGuard */ \"./components/FeatureGuard.tsx\");\n/* harmony import */ var _Components_Tag__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Tag */ \"./components/Tag.tsx\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_6__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst GiveawaySummaryTags = (param)=>{\n    let { summaries, totalPoints, totalXP, size = \"small\" } = param;\n    _s();\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)(\"ssr\", {\n        keyPrefix: \"global\"\n    });\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const [visibleTags, setVisibleTags] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [hiddenCount, setHiddenCount] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(0);\n    const formatMeta = (groupKey)=>{\n        switch(groupKey){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.ERC1155_AIR_TOKEN:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.ERC721_AIR_TOKEN:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.DOTSAMA_NFT_AIR_TOKEN:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__.Rocket, {\n                        size: 16,\n                        className: \"text-fuchsia-500\",\n                        weight: \"duotone\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, undefined),\n                    title: \"NFT\"\n                };\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.ERC20_AIR_TOKEN:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.DOTSAMA_TOKEN_AIR_TOKEN:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__.Coins, {\n                        size: 16,\n                        className: \"text-teal-500\",\n                        weight: \"duotone\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 17\n                    }, undefined),\n                    title: \"AirToken\"\n                };\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.WHITELIST:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__.Star, {\n                        size: 16,\n                        className: \"text-cyan-500\",\n                        weight: \"duotone\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 17\n                    }, undefined),\n                    title: \"Whitelist\"\n                };\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.MERCHANDISE:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__.TShirt, {\n                        size: 16,\n                        className: \"text-purple-500\",\n                        weight: \"duotone\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 13\n                    }, undefined),\n                    title: \"Merchandise\"\n                };\n        }\n    };\n    const group = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(()=>{\n        return summaries.reduce((acc, item)=>{\n            return {\n                ...acc,\n                [item.giveawayType]: [\n                    ...acc[item.giveawayType] || [],\n                    item\n                ]\n            };\n        }, {});\n    }, [\n        summaries\n    ]);\n    const allTags = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(()=>{\n        const tags = [];\n        const uniqueTitles = new Set();\n        Object.keys(group).forEach((item, index)=>{\n            const items = group[item];\n            if (item === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.ERC1155_AIR_POOL || item === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.ERC20_AIR_POOL || item === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.ERC721_AIR_POOL || item === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.DOTSAMA_TOKEN_AIR_POOL || item === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.DOTSAMA_NFT_AIR_POOL) {\n                items.forEach((summary, idx)=>{\n                    const poolTitle = (summary === null || summary === void 0 ? void 0 : summary.title) || \"\";\n                    if (!uniqueTitles.has(poolTitle)) {\n                        uniqueTitles.add(poolTitle);\n                        tags.push({\n                            id: \"pool-\".concat(index, \"-\").concat(idx),\n                            title: poolTitle,\n                            icon: (summary === null || summary === void 0 ? void 0 : summary.icon) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                src: summary.icon,\n                                width: 20,\n                                height: 20,\n                                className: \"rounded-full\",\n                                alt: poolTitle || \"Token icon\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 17\n                            }, undefined) : undefined,\n                            type: \"summary\"\n                        });\n                    }\n                });\n            } else {\n                const meta = formatMeta(item);\n                if (meta) {\n                    if (!uniqueTitles.has(meta.title)) {\n                        uniqueTitles.add(meta.title);\n                        tags.push({\n                            id: \"type-\".concat(item, \"-\").concat(index),\n                            title: meta.title,\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-5 h-5\",\n                                children: meta.icon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 21\n                            }, undefined),\n                            type: \"summary\"\n                        });\n                    }\n                }\n            }\n        });\n        if (typeof totalXP === \"number\" && totalXP > 0) {\n            tags.push({\n                id: \"xp\",\n                title: \"\".concat(totalXP, \" \").concat(globalT(\"projectXp\")),\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__.Sparkle, {\n                    size: 16,\n                    weight: \"duotone\",\n                    className: \"text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 11\n                }, undefined),\n                type: \"xp\"\n            });\n        }\n        if (typeof totalPoints === \"number\" && totalPoints > 0) {\n            tags.push({\n                id: \"points\",\n                title: \"\".concat(totalPoints, \" \").concat(globalT(\"projectPoints\")),\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__.Fire, {\n                    size: 16,\n                    weight: \"duotone\",\n                    className: \"text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 15\n                }, undefined),\n                type: \"points\"\n            });\n        }\n        return tags;\n    }, [\n        group,\n        totalXP,\n        totalPoints,\n        globalT\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        if (!containerRef.current || allTags.length === 0) return;\n        const resizeObserver = new ResizeObserver(()=>{\n            if (!containerRef.current) return;\n            const containerWidth = containerRef.current.clientWidth;\n            const moreTagWidth = 64;\n            const gap = 8;\n            // Create temporary tags to measure their widths\n            const tempDiv = document.createElement(\"div\");\n            tempDiv.style.position = \"absolute\";\n            tempDiv.style.visibility = \"hidden\";\n            tempDiv.style.display = \"flex\";\n            tempDiv.style.gap = \"\".concat(gap, \"px\");\n            document.body.appendChild(tempDiv);\n            // Create temporary elements for each tag to measure width\n            const tagWidths = allTags.map((tag)=>{\n                const elem = document.createElement(\"div\");\n                elem.className = \"tag-measure\";\n                elem.textContent = tag.title;\n                elem.style.padding = \"0 12px\";\n                elem.style.whiteSpace = \"nowrap\";\n                elem.style.fontSize = size === \"small\" ? \"12px\" : \"14px\";\n                tempDiv.appendChild(elem);\n                return elem.offsetWidth + 28;\n            });\n            document.body.removeChild(tempDiv);\n            let currentWidth = 0;\n            let visibleCount = 0;\n            let needsMoreTag = false;\n            let totalWidth = 0;\n            for(let i = 0; i < tagWidths.length; i++){\n                totalWidth += tagWidths[i];\n                if (i < tagWidths.length - 1) {\n                    totalWidth += gap;\n                }\n            }\n            needsMoreTag = totalWidth > containerWidth;\n            const availableWidth = needsMoreTag ? containerWidth - moreTagWidth - gap : containerWidth;\n            for(let i = 0; i < tagWidths.length; i++){\n                if (currentWidth + tagWidths[i] <= availableWidth) {\n                    currentWidth += tagWidths[i] + (i > 0 ? gap : 0);\n                    visibleCount++;\n                } else {\n                    break;\n                }\n            }\n            setVisibleTags(allTags.slice(0, visibleCount));\n            setHiddenCount(allTags.length - visibleCount);\n        });\n        resizeObserver.observe(containerRef.current);\n        return ()=>{\n            resizeObserver.disconnect();\n        };\n    }, [\n        allTags,\n        size\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex relative overflow-x-hidden w-full gap-2\",\n        ref: containerRef,\n        children: [\n            visibleTags.map((tag)=>{\n                if (tag.type === \"xp\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_1__.FeatureGuard, {\n                        feature: \"XP\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n                            title: tag.title,\n                            size: size,\n                            icon: tag.icon,\n                            className: \"!font-semibold\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 15\n                        }, undefined)\n                    }, tag.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 13\n                    }, undefined);\n                }\n                if (tag.type === \"points\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_1__.FeatureGuard, {\n                        feature: \"POINTS\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n                            title: tag.title,\n                            size: size,\n                            icon: tag.icon,\n                            className: \"!font-semibold\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 15\n                        }, undefined)\n                    }, tag.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 13\n                    }, undefined);\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n                    title: tag.title,\n                    size: size,\n                    icon: tag.icon,\n                    className: \"!font-semibold\"\n                }, tag.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 11\n                }, undefined);\n            }),\n            hiddenCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n                title: \"+\".concat(hiddenCount, \" more\"),\n                size: size,\n                className: \"!font-semibold !px-2 max-w-[64px]\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                lineNumber: 270,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GiveawaySummaryTags, \"sDTqu4iqTaNEUYrtQAftfgX0aqo=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation\n    ];\n});\n_c = GiveawaySummaryTags;\nvar _c;\n$RefreshReg$(_c, \"GiveawaySummaryTags\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/GiveawaySummaryTags.tsx\n"));

/***/ })

});