"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Giveaways_AirPoolGiveaway_AirPoolManualSummary_tsx";
exports.ids = ["components_Giveaways_AirPoolGiveaway_AirPoolManualSummary_tsx"];
exports.modules = {

/***/ "./components/Giveaways/AirPoolGiveaway/AirPoolManualSummary.tsx":
/*!***********************************************************************!*\
  !*** ./components/Giveaways/AirPoolGiveaway/AirPoolManualSummary.tsx ***!
  \***********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AirPoolManualSummary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Hooks_useGetBlockchain__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Hooks/useGetBlockchain */ \"./hooks/useGetBlockchain.ts\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(ethers__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_TokenGiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/TokenGiveawaySummaryItem */ \"./components/Giveaways/components/TokenGiveawaySummaryItem.tsx\");\n/* harmony import */ var _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useGetUserEventRewards */ \"./components/Giveaways/hooks/useGetUserEventRewards.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_TokenGiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_3__, _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_4__]);\n([_components_TokenGiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_3__, _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nfunction AirPoolManualSummary({ giveaway, projectEvent, onClick, size }) {\n    const giveawayInfo = giveaway.info;\n    const airPool = giveawayInfo?.airPool;\n    const asset = airPool?.asset;\n    const totalAmount = ethers__WEBPACK_IMPORTED_MODULE_2__.BigNumber.from(giveawayInfo.winnerAmount || 0);\n    const { totalClaimable, loading, totalClaimed } = (0,_hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_4__.useGetUserEventRewardsStats)(projectEvent.id, giveaway.id, totalAmount);\n    const { data: blockchainData, loading: blockchainLoading } = (0,_Hooks_useGetBlockchain__WEBPACK_IMPORTED_MODULE_1__.useGetBlockchain)(asset?.blockchainId);\n    const blockchain = blockchainData?.blockchain;\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TokenGiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            onClick: onClick,\n            size: size,\n            token: asset,\n            blockchain: blockchain,\n            amount: totalAmount,\n            claimableAmount: totalClaimable,\n            claimedAmount: totalClaimed,\n            title: t(\"giveaway.selectionTypes.manual\")\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolManualSummary.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolManualSummary.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirPoolGiveaway/AirPoolManualSummary.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/GiveawaySummaryItem.tsx":
/*!*****************************************************************!*\
  !*** ./components/Giveaways/components/GiveawaySummaryItem.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GiveawaySummaryItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction GiveawaySummaryItem({ banner, children, bannerTag, onClick, size = \"default\", actionText, className, actionSuccess }) {\n    const isVideo = banner?.endsWith(\".mp4\") || banner?.endsWith(\".webm\") || banner?.endsWith(\".mov\") || banner?.endsWith(\".gif\");\n    if (size === \"small\" || size == \"default\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `relative gap-4 grid grid-cols-[120px_1fr] items-center ${size === \"default\" ? \"sm:grid-cols-[170px_1fr]\" : \"\"} ${className || \"\"}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            height: 200,\n                            width: 200,\n                            src: banner,\n                            className: `object-cover aspect-square rounded-xl absolute top-0 left-0 w-full h-full blur-[100px]`,\n                            alt: \"giveaway-image\",\n                            unoptimized: isVideo\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 max-w-sm m-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    height: 200,\n                                    width: 200,\n                                    src: banner,\n                                    className: `object-cover w-full aspect-square rounded-xl`,\n                                    alt: \"giveaway-image\",\n                                    unoptimized: isVideo\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-1 left-0 w-full flex justify-center\",\n                                    children: bannerTag\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-20 gap-2 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: children\n                        }, void 0, false),\n                        actionText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm flex items-center gap-1 text-blue-500 font-medium cursor-pointer\",\n                            onClick: onClick,\n                            children: [\n                                actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                                    weight: \"bold\",\n                                    size: 15\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"line-clamp-1 break-all\",\n                                    children: actionText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    \"aria-hidden\": \"true\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative gap-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute transform-gpu -z-10 rounded-3xl w-full blur-3xl`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            height: 400,\n                            width: 400,\n                            src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                            className: `object-cover w-full aspect-square rounded-xl`,\n                            alt: \"giveaway-image\",\n                            unoptimized: isVideo\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pb-2 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                height: 400,\n                                width: 400,\n                                src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                                className: `object-cover aspect-square w-full rounded-xl`,\n                                alt: \"giveaway-image\",\n                                unoptimized: isVideo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2\",\n                                children: bannerTag\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-20 space-y-2\",\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"sm\",\n                            rounded: \"full\",\n                            className: \"!font-semibold mt-2\",\n                            block: true,\n                            onClick: onClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-1 items-center\",\n                                children: [\n                                    actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                                        weight: \"bold\",\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"line-clamp-1 break-all\",\n                                        children: actionText\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/GiveawaySummaryItem.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/TokenAddress.tsx":
/*!**********************************************************!*\
  !*** ./components/Giveaways/components/TokenAddress.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenAddress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction TokenAddress({ token, blockchain, className, showBlockchain = false, addressChars = 3 }) {\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    if (token.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.AssetType.NATIVE) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex gap-1.5 text-sm text-cl items-center ${className || \"\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium\",\n                children: blockchain?.type === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.BlockchainType.DOTSAMA ? token.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.AssetType.DOTSAMA_NFT ? `NFT #${token.tokenId}` : `Token #${token.tokenId}` : (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.shortenAddress)(token.address, addressChars)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                className: \"text-primary-foreground bg-primary rounded-full p-1\",\n                weight: \"bold\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Copy, {\n                className: \"cursor-pointer text-ch\",\n                size: 16,\n                onClick: (event)=>{\n                    event.stopPropagation();\n                    const textToCopy = blockchain?.type === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.BlockchainType.DOTSAMA ? token.tokenId : token.address;\n                    navigator.clipboard.writeText(textToCopy);\n                    setCopied(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this),\n            blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"inline-flex text-ch\",\n                target: \"_blank\",\n                href: blockchain.type === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.BlockchainType.DOTSAMA ? (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getExplorerLink)(blockchain.blockExplorerUrls, token.tokenId, token.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.AssetType.DOTSAMA_NFT ? _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ExplorerDataType.DOTSAMA_NFT : _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ExplorerDataType.DOTSAMA_TOKEN) : (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getExplorerLink)(blockchain.blockExplorerUrls, token.address, _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ExplorerDataType.TOKEN),\n                rel: \"noreferrer\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.ArrowSquareOut, {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this),\n            showBlockchain && blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_5___default()), {\n                src: blockchain?.icon || \"\",\n                height: 20,\n                width: 20,\n                className: \"h-5 w-5\",\n                alt: \"blockchain-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenAddress.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/TokenGiveawaySummaryItem.tsx":
/*!**********************************************************************!*\
  !*** ./components/Giveaways/components/TokenGiveawaySummaryItem.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenGiveawaySummaryItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Components_Tag__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Tag */ \"./components/Tag.tsx\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(ethers__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./GiveawaySummaryItem */ \"./components/Giveaways/components/GiveawaySummaryItem.tsx\");\n/* harmony import */ var _TokenAddress__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TokenAddress */ \"./components/Giveaways/components/TokenAddress.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_Tag__WEBPACK_IMPORTED_MODULE_2__, _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_4__, _TokenAddress__WEBPACK_IMPORTED_MODULE_5__]);\n([_Components_Tag__WEBPACK_IMPORTED_MODULE_2__, _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_4__, _TokenAddress__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nfunction TokenGiveawaySummaryItem({ token, blockchain, amount, claimableAmount, claimedAmount, onClick, size, title }) {\n    const isClaimed = amount && claimedAmount.eq(amount);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        size: size,\n        onClick: onClick,\n        banner: token.icon || \"\",\n        actionText: isClaimed ? `Claimed ${(0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatAmount)(claimedAmount?.toString() || \"\", token.decimals)} ${token.ticker} ` : claimableAmount.gt(0) ? `Claim ${(0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatAmount)(claimableAmount?.toString() || \"\", token.decimals)} ${token.ticker}` : \"Learn more\",\n        actionSuccess: isClaimed,\n        bannerTag: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n            title: blockchain?.name,\n            size: \"small\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                src: blockchain?.icon || \"\",\n                className: \"h-3 w-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawaySummaryItem.tsx\",\n                lineNumber: 51,\n                columnNumber: 17\n            }, void 0),\n            className: \"!bg-slate-900/50  backdrop-blur !font-semibold inline-flex !text-white\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawaySummaryItem.tsx\",\n            lineNumber: 48,\n            columnNumber: 9\n        }, void 0),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n                    title: title,\n                    className: \"!inline-flex !text-xs !font-semibold\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawaySummaryItem.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawaySummaryItem.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-base text-cs font-semibold line-clamp-1 break-all\",\n                children: [\n                    \"Win up to \",\n                    (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatAmount)(amount?.toString() || \"\", token.decimals),\n                    \" \",\n                    token.ticker,\n                    \" \"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawaySummaryItem.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            token.address && token.address != ethers__WEBPACK_IMPORTED_MODULE_3__.ethers.constants.AddressZero || token.tokenId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAddress__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                token: token,\n                blockchain: blockchain\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawaySummaryItem.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawaySummaryItem.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenGiveawaySummaryItem.tsx\n");

/***/ }),

/***/ "./hooks/useGetBlockchain.ts":
/*!***********************************!*\
  !*** ./hooks/useGetBlockchain.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGetBlockchain: () => (/* binding */ useGetBlockchain)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"@apollo/client\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst GET_BLOCKCHAIN = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql)`\r\n  query blockchain($id: ID!) {\r\n    blockchain(id: $id) {\r\n      id\r\n      name\r\n      chainId\r\n      icon\r\n      blockExplorerUrls\r\n      rpcUrls\r\n      nativeCurrency\r\n      decimals\r\n      type\r\n    }\r\n  }\r\n`;\nfunction useGetBlockchain(id) {\n    return (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.useQuery)(GET_BLOCKCHAIN, {\n        variables: {\n            id\n        },\n        skip: !id\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ob29rcy91c2VHZXRCbG9ja2NoYWluLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErQztBQUcvQyxNQUFNRSxpQkFBaUJGLG1EQUFHLENBQUM7Ozs7Ozs7Ozs7Ozs7O0FBYzNCLENBQUM7QUFFTSxTQUFTRyxpQkFBaUJDLEVBQVU7SUFDekMsT0FBT0gsd0RBQVFBLENBQ2JDLGdCQUNBO1FBQ0VHLFdBQVc7WUFDVEQ7UUFDRjtRQUNBRSxNQUFNLENBQUNGO0lBQ1Q7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2hvb2tzL3VzZUdldEJsb2NrY2hhaW4udHM/ZGMxYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBncWwsIHVzZVF1ZXJ5IH0gZnJvbSAnQGFwb2xsby9jbGllbnQnO1xyXG5pbXBvcnQgeyBCbG9ja2NoYWluLCBRdWVyeV9ibG9ja2NoYWluQXJncyB9IGZyb20gJ0BhaXJseWZ0L3R5cGVzJztcclxuXHJcbmNvbnN0IEdFVF9CTE9DS0NIQUlOID0gZ3FsYFxyXG4gIHF1ZXJ5IGJsb2NrY2hhaW4oJGlkOiBJRCEpIHtcclxuICAgIGJsb2NrY2hhaW4oaWQ6ICRpZCkge1xyXG4gICAgICBpZFxyXG4gICAgICBuYW1lXHJcbiAgICAgIGNoYWluSWRcclxuICAgICAgaWNvblxyXG4gICAgICBibG9ja0V4cGxvcmVyVXJsc1xyXG4gICAgICBycGNVcmxzXHJcbiAgICAgIG5hdGl2ZUN1cnJlbmN5XHJcbiAgICAgIGRlY2ltYWxzXHJcbiAgICAgIHR5cGVcclxuICAgIH1cclxuICB9XHJcbmA7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gdXNlR2V0QmxvY2tjaGFpbihpZDogc3RyaW5nKSB7XHJcbiAgcmV0dXJuIHVzZVF1ZXJ5PHsgYmxvY2tjaGFpbjogQmxvY2tjaGFpbiB9LCBRdWVyeV9ibG9ja2NoYWluQXJncz4oXHJcbiAgICBHRVRfQkxPQ0tDSEFJTixcclxuICAgIHtcclxuICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgaWQsXHJcbiAgICAgIH0sXHJcbiAgICAgIHNraXA6ICFpZCxcclxuICAgIH0sXHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiZ3FsIiwidXNlUXVlcnkiLCJHRVRfQkxPQ0tDSEFJTiIsInVzZUdldEJsb2NrY2hhaW4iLCJpZCIsInZhcmlhYmxlcyIsInNraXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./hooks/useGetBlockchain.ts\n");

/***/ })

};
;