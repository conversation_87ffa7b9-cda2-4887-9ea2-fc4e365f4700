import {
  ExplorerDataType,
  getExplorerLink,
  shortenAddress,
} from '@airlyft/web3-evm';
import { ArrowSquareOut, Check, Copy } from '@phosphor-icons/react';
import {
  AssetType,
  Blockchain,
  BlockchainAsset,
  BlockchainType,
} from '@airlyft/types';
import { useState } from 'react';
import Image from 'next/image';

export default function TokenAddress({
  token,
  blockchain,
  className,
  showBlockchain = false,
  addressChars = 3,
}: {
  token: BlockchainAsset;
  blockchain: Blockchain | undefined;
  className?: string;
  showBlockchain?: boolean;
  addressChars?: number;
}) {
  const [copied, setCopied] = useState(false);

  if (token.assetType === AssetType.NATIVE) {
    return <></>;
  }

  return (
    <div
      className={`flex gap-1.5 text-sm text-cl items-center ${className || ''}`}
    >
      <div className="font-medium">
        {blockchain?.type === BlockchainType.DOTSAMA
          ? token.assetType === AssetType.DOTSAMA_NFT
            ? `NFT #${token.tokenId}`
            : `Token #${token.tokenId}`
          : shortenAddress(token.address, addressChars)}
      </div>
      {copied ? (
        <Check
          className="text-primary-foreground bg-primary rounded-full p-1"
          weight="bold"
        />
      ) : (
        <Copy
          className="cursor-pointer text-ch"
          size={16}
          onClick={(event) => {
            event.stopPropagation();
            const textToCopy =
              blockchain?.type === BlockchainType.DOTSAMA
                ? token.tokenId
                : token.address;
            navigator.clipboard.writeText(textToCopy);
            setCopied(true);
          }}
        />
      )}
      {blockchain && (
        <a
          className="inline-flex text-ch"
          target="_blank"
          href={
            blockchain.type === BlockchainType.DOTSAMA
              ? getExplorerLink(
                  blockchain.blockExplorerUrls,
                  token.tokenId,
                  token.assetType === AssetType.DOTSAMA_NFT
                    ? ExplorerDataType.DOTSAMA_NFT
                    : ExplorerDataType.DOTSAMA_TOKEN,
                )
              : getExplorerLink(
                  blockchain.blockExplorerUrls,
                  token.address,
                  ExplorerDataType.TOKEN,
                )
          }
          rel="noreferrer"
        >
          <ArrowSquareOut size={16} />
        </a>
      )}
      {showBlockchain && blockchain && (
        <Image
          src={blockchain?.icon || ''}
          height={20}
          width={20}
          className="h-5 w-5"
          alt="blockchain-icon"
        />
      )}
    </div>
  );
}
