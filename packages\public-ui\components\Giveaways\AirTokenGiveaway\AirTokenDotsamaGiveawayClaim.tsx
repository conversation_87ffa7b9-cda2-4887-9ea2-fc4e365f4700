import { SuccessAlertBox } from '@Components/AlertBox';
import toaster from '@Components/Toaster/Toaster';
import { TransactionResult } from '@Components/TransactionResult';
import DotsamaWallet from '@Components/Web3Wallet/Dotsama/DotsamaWallet';
import { convertToSs58Address } from '@Root/helpers/dotsama';
import {
  Blockchain,
  BlockchainAsset,
  Giveaway,
  ProjectEvent,
  RewardStatus,
  Web3WalletType,
} from '@airlyft/types';
import { formatAmount } from '@airlyft/web3-evm';
import { DotsamaConnectorData } from '@airlyft/web3-evm-hooks';
import { BigNumber } from 'ethers';
import { useState } from 'react';
import GiveawayTransactionHash from '../GiveawayTransactionHash';
import { useGetUserEventRewards } from '../hooks/useGetUserEventRewards';
import { useClaimDotsamaAirTokenGiveaway } from './airtoken-giveaway.gql';
import { useTranslation } from 'next-i18next';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';
import { RecaptchaDeclaration } from '@Components/RecaptchaDeclaration';

const AirTokenDotsamaGiveawayClaim = ({
  giveaway,
  projectEvent,
  blockchain,
  airToken,
  amount,
}: {
  blockchain: Blockchain;
  giveaway: Giveaway;
  projectEvent: ProjectEvent;
  airToken: BlockchainAsset;
  amount: BigNumber;
}) => {
  const { executeRecaptcha } = useGoogleReCaptcha();
  const [claimDotsama] = useClaimDotsamaAirTokenGiveaway();
  const [isClaiming, setIsClaiming] = useState(false);
  const { t } = useTranslation('translation');
  const { data: userEventRewardsData, loading: isUserEventRewardsLoading } =
    useGetUserEventRewards(projectEvent.id);

  const processing = userEventRewardsData?.userEventRewards.find(
    (item) => item.status === RewardStatus.PROCESSING,
  );

  const txHash = [...(userEventRewardsData?.userEventRewards || [])]
    .sort((a, b) => {
      const dateA = +new Date(a.updatedAt);
      const dateB = +new Date(b.updatedAt);
      return dateB - dateA;
    })
    .find(
      (reward) => reward.txHash && giveaway.id === reward.giveawayId,
    )?.txHash;

  const handleSubmit = async (connectorData: DotsamaConnectorData) => {
    const { account } = connectorData;

    if (!account || !airToken) return;
    setIsClaiming(true);

    let captcha: string | undefined;

    if (projectEvent.ipProtect) {
      if (!executeRecaptcha) {
        toaster({
          title: 'Failed',
          text: 'Recaptcha not initialized',
          type: 'error',
        });
        setIsClaiming(false);
        return;
      }

      captcha = await executeRecaptcha('airtoken_dotsama_giveaway_claim');
    }

    try {
      const formattedAddress = convertToSs58Address(
        account,
        blockchain.chainId,
      );

      await claimDotsama({
        variables: {
          projectId: projectEvent.project.id,
          eventId: projectEvent.id,
          giveawayId: giveaway.id,
          userAddress: formattedAddress,
          captcha,
        },
      });

      toaster({
        title: 'Submitted',
        text: 'Your claim request has been submitted, check your notifications for an update.',
        type: 'success',
      });
    } catch (err: any) {
      toaster({
        title: 'Failed',
        text: err.message,
        type: 'error',
      });
    } finally {
      setIsClaiming(false);
    }
  };

  if (processing) {
    return (
      <SuccessAlertBox
        title={t('giveaway.airTokenPool.successTitle')}
        subtitle={t('giveaway.airTokenPool.successSubtitle')}
      />
    );
  }

  if (!amount.isZero()) {
    return (
      <div>
        {txHash ? (
          <TransactionResult txHash={txHash} blockchain={blockchain} />
        ) : (
          <div className="space-y-4">
            <DotsamaWallet
              blockchain={blockchain}
              button={{
                confirm: {
                  enable: true,
                  loading: isClaiming || isUserEventRewardsLoading,
                  text: `Claim ${formatAmount(
                    amount?.toString(),
                    airToken.decimals,
                  )} ${airToken.ticker} using `,
                },
              }}
              onSuccess={handleSubmit}
              excludedWallets={[Web3WalletType.DOTSAMA_MANUAL]}
            />
            {projectEvent.ipProtect && (
              <RecaptchaDeclaration className="text-xs text-cs text-center" />
            )}
          </div>
        )}
      </div>
    );
  }

  return <GiveawayTransactionHash txHash={txHash} blockchain={blockchain} />;
};

export default AirTokenDotsamaGiveawayClaim;
