"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/user/[user]/campaigns",{

/***/ "./components/Giveaways/components/GiveawaySummaryTags.tsx":
/*!*****************************************************************!*\
  !*** ./components/Giveaways/components/GiveawaySummaryTags.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GiveawaySummaryTags: function() { return /* binding */ GiveawaySummaryTags; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/FeatureGuard */ \"./components/FeatureGuard.tsx\");\n/* harmony import */ var _Components_Tag__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Tag */ \"./components/Tag.tsx\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @Root/utils/string-utils */ \"./utils/string-utils.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst GiveawaySummaryTags = (param)=>{\n    let { summaries, totalPoints, totalXP, size = \"small\" } = param;\n    _s();\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)(\"ssr\", {\n        keyPrefix: \"global\"\n    });\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const [visibleTags, setVisibleTags] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [hiddenCount, setHiddenCount] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(0);\n    const formatMeta = (groupKey)=>{\n        switch(groupKey){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.ERC1155_AIR_TOKEN:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.ERC721_AIR_TOKEN:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.DOTSAMA_NFT_AIR_TOKEN:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_8__.Rocket, {\n                        size: 16,\n                        className: \"text-fuchsia-500\",\n                        weight: \"duotone\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 13\n                    }, undefined),\n                    title: \"NFT\"\n                };\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.ERC20_AIR_TOKEN:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.DOTSAMA_TOKEN_AIR_TOKEN:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_8__.Coins, {\n                        size: 16,\n                        className: \"text-teal-500\",\n                        weight: \"duotone\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 17\n                    }, undefined),\n                    title: \"AirToken\"\n                };\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.WHITELIST:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_8__.Star, {\n                        size: 16,\n                        className: \"text-cyan-500\",\n                        weight: \"duotone\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 17\n                    }, undefined),\n                    title: \"Whitelist\"\n                };\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.MERCHANDISE:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_8__.TShirt, {\n                        size: 16,\n                        className: \"text-purple-500\",\n                        weight: \"duotone\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 13\n                    }, undefined),\n                    title: \"Merchandise\"\n                };\n        }\n    };\n    const group = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(()=>{\n        return summaries.reduce((acc, item)=>{\n            return {\n                ...acc,\n                [item.giveawayType]: [\n                    ...acc[item.giveawayType] || [],\n                    item\n                ]\n            };\n        }, {});\n    }, [\n        summaries\n    ]);\n    const allTags = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(()=>{\n        const tags = [];\n        const uniqueTitles = new Set();\n        Object.keys(group).forEach((item, index)=>{\n            const items = group[item];\n            if (item === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.ERC1155_AIR_POOL || item === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.ERC20_AIR_POOL || item === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.ERC721_AIR_POOL || item === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.DOTSAMA_TOKEN_AIR_POOL || item === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.GiveawayType.DOTSAMA_NFT_AIR_POOL) {\n                items.forEach((summary, idx)=>{\n                    const poolTitle = (summary === null || summary === void 0 ? void 0 : summary.title) || \"\";\n                    if (!uniqueTitles.has(poolTitle)) {\n                        uniqueTitles.add(poolTitle);\n                        tags.push({\n                            id: \"pool-\".concat(index, \"-\").concat(idx),\n                            title: poolTitle,\n                            icon: (summary === null || summary === void 0 ? void 0 : summary.icon) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                src: summary.icon,\n                                width: 20,\n                                height: 20,\n                                className: \"rounded-full\",\n                                alt: poolTitle || \"Token icon\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 17\n                            }, undefined) : undefined,\n                            type: \"summary\"\n                        });\n                    }\n                });\n            } else {\n                const meta = formatMeta(item);\n                if (meta) {\n                    if (!uniqueTitles.has(meta.title)) {\n                        uniqueTitles.add(meta.title);\n                        tags.push({\n                            id: \"type-\".concat(item, \"-\").concat(index),\n                            title: meta.title,\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-5 h-5\",\n                                children: meta.icon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 21\n                            }, undefined),\n                            type: \"summary\"\n                        });\n                    }\n                }\n            }\n        });\n        if (typeof totalXP === \"number\" && totalXP > 0) {\n            tags.push({\n                id: \"xp\",\n                title: \"\".concat(totalXP, \" \").concat((0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_7__.pluralize)(totalXP, globalT(\"projectXp\"), globalT(\"projectXp_many\"))),\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_8__.Sparkle, {\n                    size: 16,\n                    weight: \"duotone\",\n                    className: \"text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, undefined),\n                type: \"xp\"\n            });\n        }\n        if (typeof totalPoints === \"number\" && totalPoints > 0) {\n            tags.push({\n                id: \"points\",\n                title: \"\".concat(totalPoints, \" \").concat((0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_7__.pluralize)(totalPoints, globalT(\"projectPoints\"), globalT(\"projectPoints_many\"))),\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_8__.Fire, {\n                    size: 16,\n                    weight: \"duotone\",\n                    className: \"text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 15\n                }, undefined),\n                type: \"points\"\n            });\n        }\n        return tags;\n    }, [\n        group,\n        totalXP,\n        totalPoints,\n        globalT\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        if (!containerRef.current || allTags.length === 0) return;\n        const resizeObserver = new ResizeObserver(()=>{\n            if (!containerRef.current) return;\n            const containerWidth = containerRef.current.clientWidth;\n            const moreTagWidth = 64;\n            const gap = 8;\n            // Create temporary tags to measure their widths\n            const tempDiv = document.createElement(\"div\");\n            tempDiv.style.position = \"absolute\";\n            tempDiv.style.visibility = \"hidden\";\n            tempDiv.style.display = \"flex\";\n            tempDiv.style.gap = \"\".concat(gap, \"px\");\n            document.body.appendChild(tempDiv);\n            // Create temporary elements for each tag to measure width\n            const tagWidths = allTags.map((tag)=>{\n                const elem = document.createElement(\"div\");\n                elem.className = \"tag-measure\";\n                elem.textContent = tag.title;\n                elem.style.padding = \"0 12px\";\n                elem.style.whiteSpace = \"nowrap\";\n                elem.style.fontSize = size === \"small\" ? \"12px\" : \"14px\";\n                tempDiv.appendChild(elem);\n                return elem.offsetWidth + 28;\n            });\n            document.body.removeChild(tempDiv);\n            let currentWidth = 0;\n            let visibleCount = 0;\n            let needsMoreTag = false;\n            let totalWidth = 0;\n            for(let i = 0; i < tagWidths.length; i++){\n                totalWidth += tagWidths[i];\n                if (i < tagWidths.length - 1) {\n                    totalWidth += gap;\n                }\n            }\n            needsMoreTag = totalWidth > containerWidth;\n            const availableWidth = needsMoreTag ? containerWidth - moreTagWidth - gap : containerWidth;\n            for(let i = 0; i < tagWidths.length; i++){\n                if (currentWidth + tagWidths[i] <= availableWidth) {\n                    currentWidth += tagWidths[i] + (i > 0 ? gap : 0);\n                    visibleCount++;\n                } else {\n                    break;\n                }\n            }\n            setVisibleTags(allTags.slice(0, visibleCount));\n            setHiddenCount(allTags.length - visibleCount);\n        });\n        resizeObserver.observe(containerRef.current);\n        return ()=>{\n            resizeObserver.disconnect();\n        };\n    }, [\n        allTags,\n        size\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex relative overflow-x-hidden w-full gap-2\",\n        ref: containerRef,\n        children: [\n            visibleTags.map((tag)=>{\n                if (tag.type === \"xp\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_1__.FeatureGuard, {\n                        feature: \"XP\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n                            title: tag.title,\n                            size: size,\n                            icon: tag.icon,\n                            className: \"!font-semibold\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 15\n                        }, undefined)\n                    }, tag.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 13\n                    }, undefined);\n                }\n                if (tag.type === \"points\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_1__.FeatureGuard, {\n                        feature: \"POINTS\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n                            title: tag.title,\n                            size: size,\n                            icon: tag.icon,\n                            className: \"!font-semibold\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 15\n                        }, undefined)\n                    }, tag.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 13\n                    }, undefined);\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n                    title: tag.title,\n                    size: size,\n                    icon: tag.icon,\n                    className: \"!font-semibold\"\n                }, tag.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 11\n                }, undefined);\n            }),\n            hiddenCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n                title: \"+\".concat(hiddenCount, \" more\"),\n                size: size,\n                className: \"!font-semibold !px-2 max-w-[64px]\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n                lineNumber: 279,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryTags.tsx\",\n        lineNumber: 236,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GiveawaySummaryTags, \"sDTqu4iqTaNEUYrtQAftfgX0aqo=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation\n    ];\n});\n_c = GiveawaySummaryTags;\nvar _c;\n$RefreshReg$(_c, \"GiveawaySummaryTags\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/GiveawaySummaryTags.tsx\n"));

/***/ })

});