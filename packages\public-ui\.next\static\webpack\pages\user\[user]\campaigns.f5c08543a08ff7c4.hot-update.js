"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/user/[user]/campaigns",{

/***/ "./components/CookieBanner.tsx":
/*!*************************************!*\
  !*** ./components/CookieBanner.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CookieBanner; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Button */ \"./components/Button.tsx\");\n/* harmony import */ var _Hooks_useUserDetails__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Hooks/useUserDetails */ \"./hooks/useUserDetails.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _Toaster_Toaster__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _Hooks_useUpdateUserCookieConsent__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @Hooks/useUpdateUserCookieConsent */ \"./hooks/useUpdateUserCookieConsent.ts\");\n/* harmony import */ var _Root_services_tracking__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @Root/services/tracking */ \"./services/tracking.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction CookieBanner() {\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDetails, setShowDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { data, loading } = (0,_Hooks_useUserDetails__WEBPACK_IMPORTED_MODULE_4__.useUserDetails)();\n    const [updateUserCookieConsent] = (0,_Hooks_useUpdateUserCookieConsent__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    const { signUpTrack } = (0,_Root_services_tracking__WEBPACK_IMPORTED_MODULE_8__.useGtmTrack)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _data_me_onboarded, _data_me, _data_me1;\n        if (loading) return;\n        var _data_me_onboarded_includes;\n        const serverOnboarded = (_data_me_onboarded_includes = data === null || data === void 0 ? void 0 : (_data_me = data.me) === null || _data_me === void 0 ? void 0 : (_data_me_onboarded = _data_me.onboarded) === null || _data_me_onboarded === void 0 ? void 0 : _data_me_onboarded.includes(_airlyft_types__WEBPACK_IMPORTED_MODULE_5__.Onboarding.PARTICIPANT_TERMS)) !== null && _data_me_onboarded_includes !== void 0 ? _data_me_onboarded_includes : false;\n        const serverConsent = data === null || data === void 0 ? void 0 : (_data_me1 = data.me) === null || _data_me1 === void 0 ? void 0 : _data_me1.cookieConsent;\n        const shouldShow = serverOnboarded && !serverConsent;\n        let timer;\n        if (shouldShow) {\n            timer = setTimeout(()=>setIsVisible(true), 1500);\n        }\n        return ()=>{\n            if (timer) clearTimeout(timer);\n        };\n    }, [\n        data,\n        loading\n    ]);\n    const cookieOnboarding = (consent)=>{\n        updateUserCookieConsent({\n            variables: {\n                cookieConsent: consent\n            },\n            onCompleted: ()=>{\n                setIsVisible(false);\n                signUpTrack(consent);\n            },\n            onError: ()=>{\n                (0,_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_6__[\"default\"])({\n                    title: \"Update failed\",\n                    text: \"Unable to update cookie consent\",\n                    type: \"error\"\n                });\n            }\n        });\n    };\n    const dismiss = ()=>{\n        setIsVisible(false);\n    };\n    const toggleDetails = ()=>{\n        setShowDetails(!showDetails);\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-5 left-0 !z-[99999] right-0 mx-auto max-w-4xl px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"backdrop-blur-lg bg-background/80  border border-border/60 rounded-2xl shadow-xl shadow-primary/20 overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 right-5 w-32 h-32 bg-primary/5 rounded-full blur-3xl -z-10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-10 w-24 h-24 bg-blue-500/5 rounded-full blur-3xl -z-10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-5 md:p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: dismiss,\n                                className: \"absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\",\n                                \"aria-label\": \"Dismiss cookie banner\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_9__.X, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row items-start md:items-center gap-5 md:gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 p-3 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 text-primary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_9__.Cookie, {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-grow\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-base font-semibold mb-1.5 flex items-center\",\n                                                children: [\n                                                    \"Cookie Preferences\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-xs py-0.5 px-1.5 bg-primary/10 text-primary rounded-full\",\n                                                        children: \"Privacy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400 max-w-2xl\",\n                                                children: \"We use cookies to enhance your browsing experience and analyze our traffic. You can choose which cookies you want to allow.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-4 space-y-3 overflow-hidden transition-all duration-300\", showDetails ? \"max-h-96 opacity-100\" : \"max-h-0 opacity-0\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 rounded-lg bg-card border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-1.5 bg-green-100 dark:bg-green-900/30 rounded-full\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_9__.Check, {\n                                                                            className: \"h-3.5 w-3.5 text-green-600 dark:text-green-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                                            lineNumber: 111,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                                        lineNumber: 110,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm font-medium\",\n                                                                                children: \"Necessary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                                                lineNumber: 114,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                                children: \"Required for basic functionality\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                                                lineNumber: 115,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                                        lineNumber: 113,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                                lineNumber: 109,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-2 py-0.5 text-xs bg-primary/20 rounded\",\n                                                                children: \"Always On\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                                lineNumber: 120,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 rounded-lg bg-card border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-1.5 bg-blue-100 dark:bg-blue-900/30 rounded-full\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_9__.Gear, {\n                                                                            className: \"h-3.5 w-3.5 text-blue-600 dark:text-blue-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                                            lineNumber: 128,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                                        lineNumber: 127,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm font-medium\",\n                                                                                children: \"Analytics\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                                                lineNumber: 131,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                                children: \"Help us improve our website\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                                                lineNumber: 132,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                                        lineNumber: 130,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-2 py-0.5 text-xs bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded\",\n                                                                children: \"Optional\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleDetails,\n                                                className: \"text-xs text-primary hover:text-primary/80 underline-offset-2 hover:underline mt-2 inline-flex items-center\",\n                                                children: [\n                                                    showDetails ? \"Hide details\" : \"View cookie details\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1 transition-transform duration-200 \".concat(showDetails ? \"rotate-180\" : \"\"),\n                                                        children: \"▼\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center gap-2 w-full md:w-[200px] md:flex-shrink-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                size: \"small\",\n                                                className: \"text-xs w-full rounded-lg py-2\",\n                                                onClick: ()=>cookieOnboarding(_airlyft_types__WEBPACK_IMPORTED_MODULE_5__.CookieConsent.ACCEPT_ALL),\n                                                children: \"Accept All\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                size: \"small\",\n                                                className: \"text-xs text-foreground w-full rounded-lg bg-card border py-2\",\n                                                onClick: ()=>cookieOnboarding(_airlyft_types__WEBPACK_IMPORTED_MODULE_5__.CookieConsent.NECESSARY),\n                                                children: \"Necessary Only\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-[2px] bg-gradient-to-r from-transparent via-primary/20 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\CookieBanner.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n_s(CookieBanner, \"DiRvyaXHDeWH1UNcW+wo1QSwZBg=\", false, function() {\n    return [\n        _Hooks_useUserDetails__WEBPACK_IMPORTED_MODULE_4__.useUserDetails,\n        _Hooks_useUpdateUserCookieConsent__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        _Root_services_tracking__WEBPACK_IMPORTED_MODULE_8__.useGtmTrack\n    ];\n});\n_c = CookieBanner;\nvar _c;\n$RefreshReg$(_c, \"CookieBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/CookieBanner.tsx\n"));

/***/ })

});