export enum ExplorerDataType {
  TRANSACTION = "transaction",
  TOKEN = "token",
  ADDRESS = "address",
  BLOCK = "block",
  EXTRINSIC = "extrinsic",
  DOTSAMA_NFT = "dotsama_nft",
  DOTSAMA_TOKEN = "dotsama_token",
}

export function getExplorerLink(
  blockExplorerUrls: Array<string>,
  data: string,
  type: ExplorerDataType
): string {
  if (blockExplorerUrls.length == 0) throw Error("Invalid urls");

  const prefix = blockExplorerUrls[0];

  switch (type) {
    case ExplorerDataType.TRANSACTION:
      return `${prefix}/tx/${data}`;

    case ExplorerDataType.TOKEN:
      return `${prefix}/token/${data}`;

    case ExplorerDataType.BLOCK:
      return `${prefix}/block/${data}`;

    case ExplorerDataType.ADDRESS:
      return `${prefix}/address/${data}`;

    case ExplorerDataType.EXTRINSIC:
      return `${prefix}/extrinsic/${data}`;

    case ExplorerDataType.DOTSAMA_NFT:
      return `${prefix}/nft_item/${data}`;

    case ExplorerDataType.DOTSAMA_TOKEN:
      return `${prefix}/assets/${data}`;

    default:
      return `${prefix}`;
  }
}
