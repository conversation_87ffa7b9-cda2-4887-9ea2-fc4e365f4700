import { Blockchain, BlockchainAsset } from '@airlyft/types';
import { formatAmount } from '@airlyft/web3-evm';
import { Tag } from '@Components/Tag';
import { BigNumber, ethers } from 'ethers';
import GiveawaySummaryItem from './GiveawaySummaryItem';
import TokenAddress from './TokenAddress';

export default function TokenGiveawaySummaryItem({
  token,
  blockchain,
  amount,
  claimableAmount,
  claimedAmount,
  onClick,
  size,
  title,
}: {
  token: BlockchainAsset;
  blockchain: Blockchain | undefined;
  amount: BigNumber | undefined;
  claimableAmount: BigNumber;
  claimedAmount: BigNumber;
  title: string;
  onClick?: () => void;
  size?: 'small' | 'default' | 'large';
}) {
  const isClaimed = amount && claimedAmount.eq(amount);
  return (
    <GiveawaySummaryItem
      size={size}
      onClick={onClick}
      banner={token.icon || ''}
      actionText={
        isClaimed
          ? `Claimed ${formatAmount(
              claimedAmount?.toString() || '',
              token.decimals,
            )} ${token.ticker} `
          : claimableAmount.gt(0)
          ? `Claim ${formatAmount(
              claimableAmount?.toString() || '',
              token.decimals,
            )} ${token.ticker}`
          : 'Learn more'
      }
      actionSuccess={isClaimed}
      bannerTag={
        <Tag
          title={blockchain?.name}
          size="small"
          icon={<img src={blockchain?.icon || ''} className="h-3 w-3" />}
          className="!bg-slate-900/50  backdrop-blur !font-semibold inline-flex !text-white"
        />
      }
    >
      <div>
        <Tag title={title} className="!inline-flex !text-xs !font-semibold" />
      </div>

      <h2 className="text-base text-cs font-semibold line-clamp-1 break-all">
        Win up to {formatAmount(amount?.toString() || '', token.decimals)}{' '}
        {token.ticker}{' '}
      </h2>

      {(token.address && token.address != ethers.constants.AddressZero) ||
      token.tokenId ? (
        <TokenAddress token={token} blockchain={blockchain} />
      ) : (
        <></>
      )}
    </GiveawaySummaryItem>
  );
}
