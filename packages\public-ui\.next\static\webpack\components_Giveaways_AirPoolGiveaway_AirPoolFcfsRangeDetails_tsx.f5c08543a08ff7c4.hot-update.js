"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("components_Giveaways_AirPoolGiveaway_AirPoolFcfsRangeDetails_tsx",{

/***/ "./components/Giveaways/components/GiveawaySummaryItem.tsx":
/*!*****************************************************************!*\
  !*** ./components/Giveaways/components/GiveawaySummaryItem.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GiveawaySummaryItem; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nfunction GiveawaySummaryItem(param) {\n    let { banner, children, bannerTag, onClick, size = \"default\", actionText, className, actionSuccess } = param;\n    const isVideo = (banner === null || banner === void 0 ? void 0 : banner.endsWith(\".mp4\")) || (banner === null || banner === void 0 ? void 0 : banner.endsWith(\".webm\")) || (banner === null || banner === void 0 ? void 0 : banner.endsWith(\".mov\")) || (banner === null || banner === void 0 ? void 0 : banner.endsWith(\".gif\"));\n    if (size === \"small\" || size == \"default\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative gap-4 grid grid-cols-[120px_1fr] items-center \".concat(size === \"default\" ? \"sm:grid-cols-[170px_1fr]\" : \"\", \" \").concat(className || \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            height: 200,\n                            width: 200,\n                            src: banner,\n                            className: \"object-cover aspect-square rounded-xl absolute top-0 left-0 w-full h-full blur-[100px]\",\n                            alt: \"giveaway-image\",\n                            unoptimized: isVideo\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 max-w-sm m-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    height: 200,\n                                    width: 200,\n                                    src: banner,\n                                    className: \"object-cover w-full aspect-square rounded-xl\",\n                                    alt: \"giveaway-image\",\n                                    unoptimized: isVideo\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-1 left-0 w-full flex justify-center\",\n                                    children: bannerTag\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-20 gap-2 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: children\n                        }, void 0, false),\n                        actionText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm flex items-center gap-1 text-blue-500 font-medium cursor-pointer\",\n                            onClick: onClick,\n                            children: [\n                                actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                                    weight: \"bold\",\n                                    size: 15\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"line-clamp-1 break-all\",\n                                    children: actionText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    \"aria-hidden\": \"true\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative gap-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute transform-gpu -z-10 rounded-3xl w-full blur-3xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            height: 400,\n                            width: 400,\n                            src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                            className: \"object-cover w-full aspect-square rounded-xl\",\n                            alt: \"giveaway-image\",\n                            unoptimized: isVideo\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pb-2 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                height: 400,\n                                width: 400,\n                                src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                                className: \"object-cover aspect-square w-full rounded-xl\",\n                                alt: \"giveaway-image\",\n                                unoptimized: isVideo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2\",\n                                children: bannerTag\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-20 space-y-2\",\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"sm\",\n                            rounded: \"full\",\n                            className: \"!font-semibold mt-2\",\n                            block: true,\n                            onClick: onClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-1 items-center\",\n                                children: [\n                                    actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                                        weight: \"bold\",\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"line-clamp-1 break-all\",\n                                        children: actionText\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_c = GiveawaySummaryItem;\nvar _c;\n$RefreshReg$(_c, \"GiveawaySummaryItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/GiveawaySummaryItem.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/components/TokenAddress.tsx":
/*!**********************************************************!*\
  !*** ./components/Giveaways/components/TokenAddress.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TokenAddress; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction TokenAddress(param) {\n    let { token, blockchain, className, showBlockchain = false, addressChars = 3 } = param;\n    _s();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    if (token.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.AssetType.NATIVE) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-1.5 text-sm text-cl items-center \".concat(className || \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium\",\n                children: (blockchain === null || blockchain === void 0 ? void 0 : blockchain.type) === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.BlockchainType.DOTSAMA ? token.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.AssetType.DOTSAMA_NFT ? \"NFT #\".concat(token.tokenId) : \"Token #\".concat(token.tokenId) : (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.shortenAddress)(token.address, addressChars)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__.Check, {\n                className: \"text-primary-foreground bg-primary rounded-full p-1\",\n                weight: \"bold\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__.Copy, {\n                className: \"cursor-pointer text-ch\",\n                size: 16,\n                onClick: (event)=>{\n                    event.stopPropagation();\n                    const textToCopy = (blockchain === null || blockchain === void 0 ? void 0 : blockchain.type) === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.BlockchainType.DOTSAMA ? token.tokenId : token.address;\n                    navigator.clipboard.writeText(textToCopy);\n                    setCopied(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this),\n            blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"inline-flex text-ch\",\n                target: \"_blank\",\n                href: blockchain.type === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.BlockchainType.DOTSAMA ? (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getExplorerLink)(blockchain.blockExplorerUrls, token.tokenId, token.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.AssetType.DOTSAMA_NFT ? _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ExplorerDataType.DOTSAMA_NFT : _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ExplorerDataType.DOTSAMA_TOKEN) : (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getExplorerLink)(blockchain.blockExplorerUrls, token.address, _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ExplorerDataType.TOKEN),\n                rel: \"noreferrer\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__.ArrowSquareOut, {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this),\n            showBlockchain && blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                src: (blockchain === null || blockchain === void 0 ? void 0 : blockchain.icon) || \"\",\n                height: 20,\n                width: 20,\n                className: \"h-5 w-5\",\n                alt: \"blockchain-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(TokenAddress, \"NE86rL3vg4NVcTTWDavsT0hUBJs=\");\n_c = TokenAddress;\nvar _c;\n$RefreshReg$(_c, \"TokenAddress\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenAddress.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/components/TokenGiveawayDetails.tsx":
/*!******************************************************************!*\
  !*** ./components/Giveaways/components/TokenGiveawayDetails.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TokenGiveawayDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Panel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Panel */ \"./components/Panel.tsx\");\n/* harmony import */ var _Components_Tag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Tag */ \"./components/Tag.tsx\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"./node_modules/ethers/lib.esm/index.js\");\n/* harmony import */ var _components_TokenGiveawayClaimStats__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/TokenGiveawayClaimStats */ \"./components/Giveaways/components/TokenGiveawayClaimStats.tsx\");\n/* harmony import */ var _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./GiveawaySummaryItem */ \"./components/Giveaways/components/GiveawaySummaryItem.tsx\");\n/* harmony import */ var _TokenAddress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TokenAddress */ \"./components/Giveaways/components/TokenAddress.tsx\");\n\n\n\n\n\n\n\n\n\nfunction TokenGiveawayDetails(param) {\n    let { token, blockchain, amount, claimableAmount, claimedAmount, expandable, expanded, children, onClick, title } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Panel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        expandable: expandable,\n        expanded: expanded,\n        onClick: onClick,\n        header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            banner: token.icon || \"\",\n            className: expanded ? \"!grid-cols-1 sm:!grid-cols-[170px_1fr]\" : \"\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_4__.Tag, {\n                        title: title,\n                        className: \"!inline-flex !text-xs !font-semibold\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 11\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-lg text-ch font-semibold break-all\",\n                    children: [\n                        \"Win up to \",\n                        (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatAmount)((amount === null || amount === void 0 ? void 0 : amount.toString()) || \"\", token.decimals),\n                        \" \",\n                        token.ticker,\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 11\n                }, void 0),\n                token.tokenId || token.address && token.address !== ethers__WEBPACK_IMPORTED_MODULE_8__.ethers.constants.AddressZero ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAddress__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    token: token,\n                    blockchain: blockchain,\n                    showBlockchain: true,\n                    className: \"mb-1\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 13\n                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TokenGiveawayClaimStats__WEBPACK_IMPORTED_MODULE_5__.TokenGiveawayClaimStats, {\n                    token: token,\n                    totalClaimable: claimableAmount,\n                    totalClaimed: claimedAmount,\n                    blockchain: blockchain\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n            lineNumber: 41,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 relative z-50\",\n            children: [\n                children,\n                amount && claimedAmount.eq(amount) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_2__.SuccessAlertBox, {\n                    title: \"Congrats!\",\n                    subtitle: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Your transaction was submitted successfully, sometimes it takes 30-60 seconds for the explorer to index it.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_c = TokenGiveawayDetails;\nvar _c;\n$RefreshReg$(_c, \"TokenGiveawayDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenGiveawayDetails.tsx\n"));

/***/ })

});