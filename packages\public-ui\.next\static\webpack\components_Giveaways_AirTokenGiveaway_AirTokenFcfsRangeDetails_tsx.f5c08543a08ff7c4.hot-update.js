"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("components_Giveaways_AirTokenGiveaway_AirTokenFcfsRangeDetails_tsx",{

/***/ "./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx":
/*!********************************************************************************!*\
  !*** ./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx ***!
  \********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _Components_TransactionResult__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/TransactionResult */ \"./components/TransactionResult.tsx\");\n/* harmony import */ var _Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Web3Wallet/Dotsama/DotsamaWallet */ \"./components/Web3Wallet/Dotsama/DotsamaWallet.tsx\");\n/* harmony import */ var _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Root/helpers/dotsama */ \"./helpers/dotsama.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../GiveawayTransactionHash */ \"./components/Giveaways/GiveawayTransactionHash.tsx\");\n/* harmony import */ var _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../hooks/useGetUserEventRewards */ \"./components/Giveaways/hooks/useGetUserEventRewards.ts\");\n/* harmony import */ var _airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./airtoken-giveaway.gql */ \"./components/Giveaways/AirTokenGiveaway/airtoken-giveaway.gql.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-google-recaptcha-v3 */ \"./node_modules/react-google-recaptcha-v3/dist/react-google-recaptcha-v3.esm.js\");\n/* harmony import */ var _Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @Components/RecaptchaDeclaration */ \"./components/RecaptchaDeclaration.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AirTokenDotsamaGiveawayClaim = (param)=>{\n    let { giveaway, projectEvent, blockchain, airToken, amount } = param;\n    var _sort_find;\n    _s();\n    const { executeRecaptcha } = (0,react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_13__.useGoogleReCaptcha)();\n    const [claimDotsama] = (0,_airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_11__.useClaimDotsamaAirTokenGiveaway)();\n    const [isClaiming, setIsClaiming] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_12__.useTranslation)(\"translation\");\n    const { data: userEventRewardsData, loading: isUserEventRewardsLoading } = (0,_hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_10__.useGetUserEventRewards)(projectEvent.id);\n    const processing = userEventRewardsData === null || userEventRewardsData === void 0 ? void 0 : userEventRewardsData.userEventRewards.find((item)=>item.status === _airlyft_types__WEBPACK_IMPORTED_MODULE_6__.RewardStatus.PROCESSING);\n    const txHash = (_sort_find = [\n        ...(userEventRewardsData === null || userEventRewardsData === void 0 ? void 0 : userEventRewardsData.userEventRewards) || []\n    ].sort((a, b)=>{\n        const dateA = +new Date(a.updatedAt);\n        const dateB = +new Date(b.updatedAt);\n        return dateB - dateA;\n    }).find((reward)=>reward.txHash && giveaway.id === reward.giveawayId)) === null || _sort_find === void 0 ? void 0 : _sort_find.txHash;\n    const handleSubmit = async (connectorData)=>{\n        const { account } = connectorData;\n        if (!account || !airToken) return;\n        setIsClaiming(true);\n        let captcha;\n        if (projectEvent.ipProtect) {\n            if (!executeRecaptcha) {\n                (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                    title: \"Failed\",\n                    text: \"Recaptcha not initialized\",\n                    type: \"error\"\n                });\n                setIsClaiming(false);\n                return;\n            }\n            captcha = await executeRecaptcha(\"airtoken_dotsama_giveaway_claim\");\n        }\n        try {\n            const formattedAddress = (0,_Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_5__.convertToSs58Address)(account, blockchain.chainId);\n            await claimDotsama({\n                variables: {\n                    projectId: projectEvent.project.id,\n                    eventId: projectEvent.id,\n                    giveawayId: giveaway.id,\n                    userAddress: formattedAddress,\n                    captcha\n                }\n            });\n            (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                title: \"Submitted\",\n                text: \"Your claim request has been submitted, check your notifications for an update.\",\n                type: \"success\"\n            });\n        } catch (err) {\n            (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                title: \"Failed\",\n                text: err.message,\n                type: \"error\"\n            });\n        } finally{\n            setIsClaiming(false);\n        }\n    };\n    if (processing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__.SuccessAlertBox, {\n            title: t(\"giveaway.airTokenPool.successTitle\"),\n            subtitle: t(\"giveaway.airTokenPool.successSubtitle\")\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!amount.isZero()) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: txHash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TransactionResult__WEBPACK_IMPORTED_MODULE_3__.TransactionResult, {\n                txHash: txHash,\n                blockchain: blockchain\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n                lineNumber: 126,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        blockchain: blockchain,\n                        button: {\n                            confirm: {\n                                enable: true,\n                                loading: isClaiming || isUserEventRewardsLoading,\n                                text: \"Claim \".concat((0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_7__.formatAmount)(amount === null || amount === void 0 ? void 0 : amount.toString(), airToken.decimals), \" \").concat(airToken.ticker, \" using \")\n                            }\n                        },\n                        onSuccess: handleSubmit,\n                        excludedWallets: [\n                            _airlyft_types__WEBPACK_IMPORTED_MODULE_6__.Web3WalletType.DOTSAMA_MANUAL\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 13\n                    }, undefined),\n                    projectEvent.ipProtect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_14__.RecaptchaDeclaration, {\n                        className: \"text-xs text-cs text-center\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 15\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n                lineNumber: 128,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        txHash: txHash,\n        blockchain: blockchain\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n        lineNumber: 153,\n        columnNumber: 10\n    }, undefined);\n};\n_s(AirTokenDotsamaGiveawayClaim, \"mdMu2YwNR2p0kya76ZQkFiX65Uc=\", false, function() {\n    return [\n        react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_13__.useGoogleReCaptcha,\n        _airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_11__.useClaimDotsamaAirTokenGiveaway,\n        next_i18next__WEBPACK_IMPORTED_MODULE_12__.useTranslation,\n        _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_10__.useGetUserEventRewards\n    ];\n});\n_c = AirTokenDotsamaGiveawayClaim;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AirTokenDotsamaGiveawayClaim);\nvar _c;\n$RefreshReg$(_c, \"AirTokenDotsamaGiveawayClaim\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/components/GiveawaySummaryItem.tsx":
/*!*****************************************************************!*\
  !*** ./components/Giveaways/components/GiveawaySummaryItem.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GiveawaySummaryItem; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nfunction GiveawaySummaryItem(param) {\n    let { banner, children, bannerTag, onClick, size = \"default\", actionText, className, actionSuccess } = param;\n    const isVideo = (banner === null || banner === void 0 ? void 0 : banner.endsWith(\".mp4\")) || (banner === null || banner === void 0 ? void 0 : banner.endsWith(\".webm\")) || (banner === null || banner === void 0 ? void 0 : banner.endsWith(\".mov\")) || (banner === null || banner === void 0 ? void 0 : banner.endsWith(\".gif\"));\n    if (size === \"small\" || size == \"default\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative gap-4 grid grid-cols-[120px_1fr] items-center \".concat(size === \"default\" ? \"sm:grid-cols-[170px_1fr]\" : \"\", \" \").concat(className || \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            height: 200,\n                            width: 200,\n                            src: banner,\n                            className: \"object-cover aspect-square rounded-xl absolute top-0 left-0 w-full h-full blur-[100px]\",\n                            alt: \"giveaway-image\",\n                            unoptimized: isVideo\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 max-w-sm m-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    height: 200,\n                                    width: 200,\n                                    src: banner,\n                                    className: \"object-cover w-full aspect-square rounded-xl\",\n                                    alt: \"giveaway-image\",\n                                    unoptimized: isVideo\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-1 left-0 w-full flex justify-center\",\n                                    children: bannerTag\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-20 gap-2 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: children\n                        }, void 0, false),\n                        actionText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm flex items-center gap-1 text-blue-500 font-medium cursor-pointer\",\n                            onClick: onClick,\n                            children: [\n                                actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                                    weight: \"bold\",\n                                    size: 15\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"line-clamp-1 break-all\",\n                                    children: actionText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    \"aria-hidden\": \"true\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative gap-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute transform-gpu -z-10 rounded-3xl w-full blur-3xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            height: 400,\n                            width: 400,\n                            src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                            className: \"object-cover w-full aspect-square rounded-xl\",\n                            alt: \"giveaway-image\",\n                            unoptimized: isVideo\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pb-2 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                height: 400,\n                                width: 400,\n                                src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                                className: \"object-cover aspect-square w-full rounded-xl\",\n                                alt: \"giveaway-image\",\n                                unoptimized: isVideo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2\",\n                                children: bannerTag\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-20 space-y-2\",\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"sm\",\n                            rounded: \"full\",\n                            className: \"!font-semibold mt-2\",\n                            block: true,\n                            onClick: onClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-1 items-center\",\n                                children: [\n                                    actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                                        weight: \"bold\",\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"line-clamp-1 break-all\",\n                                        children: actionText\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_c = GiveawaySummaryItem;\nvar _c;\n$RefreshReg$(_c, \"GiveawaySummaryItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/GiveawaySummaryItem.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/components/TokenAddress.tsx":
/*!**********************************************************!*\
  !*** ./components/Giveaways/components/TokenAddress.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TokenAddress; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction TokenAddress(param) {\n    let { token, blockchain, className, showBlockchain = false, addressChars = 3 } = param;\n    _s();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    if (token.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.AssetType.NATIVE) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-1.5 text-sm text-cl items-center \".concat(className || \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium\",\n                children: (blockchain === null || blockchain === void 0 ? void 0 : blockchain.type) === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.BlockchainType.DOTSAMA ? token.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.AssetType.DOTSAMA_NFT ? \"NFT #\".concat(token.tokenId) : \"Token #\".concat(token.tokenId) : (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.shortenAddress)(token.address, addressChars)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__.Check, {\n                className: \"text-primary-foreground bg-primary rounded-full p-1\",\n                weight: \"bold\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__.Copy, {\n                className: \"cursor-pointer text-ch\",\n                size: 16,\n                onClick: (event)=>{\n                    event.stopPropagation();\n                    const textToCopy = (blockchain === null || blockchain === void 0 ? void 0 : blockchain.type) === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.BlockchainType.DOTSAMA ? token.tokenId : token.address;\n                    navigator.clipboard.writeText(textToCopy);\n                    setCopied(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this),\n            blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"inline-flex text-ch\",\n                target: \"_blank\",\n                href: blockchain.type === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.BlockchainType.DOTSAMA ? (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getExplorerLink)(blockchain.blockExplorerUrls, token.tokenId, token.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.AssetType.DOTSAMA_NFT ? _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ExplorerDataType.DOTSAMA_NFT : _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ExplorerDataType.DOTSAMA_TOKEN) : (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getExplorerLink)(blockchain.blockExplorerUrls, token.address, _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ExplorerDataType.TOKEN),\n                rel: \"noreferrer\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__.ArrowSquareOut, {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this),\n            showBlockchain && blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                src: (blockchain === null || blockchain === void 0 ? void 0 : blockchain.icon) || \"\",\n                height: 20,\n                width: 20,\n                className: \"h-5 w-5\",\n                alt: \"blockchain-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(TokenAddress, \"NE86rL3vg4NVcTTWDavsT0hUBJs=\");\n_c = TokenAddress;\nvar _c;\n$RefreshReg$(_c, \"TokenAddress\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenAddress.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/components/TokenGiveawayDetails.tsx":
/*!******************************************************************!*\
  !*** ./components/Giveaways/components/TokenGiveawayDetails.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TokenGiveawayDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Panel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Panel */ \"./components/Panel.tsx\");\n/* harmony import */ var _Components_Tag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Tag */ \"./components/Tag.tsx\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"./node_modules/ethers/lib.esm/index.js\");\n/* harmony import */ var _components_TokenGiveawayClaimStats__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/TokenGiveawayClaimStats */ \"./components/Giveaways/components/TokenGiveawayClaimStats.tsx\");\n/* harmony import */ var _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./GiveawaySummaryItem */ \"./components/Giveaways/components/GiveawaySummaryItem.tsx\");\n/* harmony import */ var _TokenAddress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TokenAddress */ \"./components/Giveaways/components/TokenAddress.tsx\");\n\n\n\n\n\n\n\n\n\nfunction TokenGiveawayDetails(param) {\n    let { token, blockchain, amount, claimableAmount, claimedAmount, expandable, expanded, children, onClick, title } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Panel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        expandable: expandable,\n        expanded: expanded,\n        onClick: onClick,\n        header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            banner: token.icon || \"\",\n            className: expanded ? \"!grid-cols-1 sm:!grid-cols-[170px_1fr]\" : \"\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_4__.Tag, {\n                        title: title,\n                        className: \"!inline-flex !text-xs !font-semibold\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 11\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-lg text-ch font-semibold break-all\",\n                    children: [\n                        \"Win up to \",\n                        (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatAmount)((amount === null || amount === void 0 ? void 0 : amount.toString()) || \"\", token.decimals),\n                        \" \",\n                        token.ticker,\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 11\n                }, void 0),\n                token.tokenId || token.address && token.address !== ethers__WEBPACK_IMPORTED_MODULE_8__.ethers.constants.AddressZero ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAddress__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    token: token,\n                    blockchain: blockchain,\n                    showBlockchain: true,\n                    className: \"mb-1\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 13\n                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TokenGiveawayClaimStats__WEBPACK_IMPORTED_MODULE_5__.TokenGiveawayClaimStats, {\n                    token: token,\n                    totalClaimable: claimableAmount,\n                    totalClaimed: claimedAmount,\n                    blockchain: blockchain\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n            lineNumber: 41,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 relative z-50\",\n            children: [\n                children,\n                amount && claimedAmount.eq(amount) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_2__.SuccessAlertBox, {\n                    title: \"Congrats!\",\n                    subtitle: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Your transaction was submitted successfully, sometimes it takes 30-60 seconds for the explorer to index it.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_c = TokenGiveawayDetails;\nvar _c;\n$RefreshReg$(_c, \"TokenGiveawayDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenGiveawayDetails.tsx\n"));

/***/ })

});