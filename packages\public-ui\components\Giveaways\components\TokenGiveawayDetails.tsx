import { Blockchain, BlockchainAsset } from '@airlyft/types';
import { formatAmount } from '@airlyft/web3-evm';
import { SuccessAlertBox } from '@Components/AlertBox';
import Panel from '@Components/Panel';
import { Tag } from '@Components/Tag';

import { BigNumber, ethers } from 'ethers';
import { TokenGiveawayClaimStats } from '../components/TokenGiveawayClaimStats';
import GiveawaySummaryItem from './GiveawaySummaryItem';
import TokenAddress from './TokenAddress';

export default function TokenGiveawayDetails({
  token,
  blockchain,
  amount,
  claimableAmount,
  claimedAmount,
  expandable,
  expanded,
  children,
  onClick,
  title,
}: {
  token: BlockchainAsset;
  blockchain: Blockchain | undefined;
  amount: BigNumber | undefined;
  claimableAmount: BigNumber;
  claimedAmount: BigNumber;
  title: string;
  expandable?: boolean;
  expanded?: boolean;
  children: React.ReactNode | undefined;
  onClick?: () => void;
}) {
  return (
    <Panel
      expandable={expandable}
      expanded={expanded}
      onClick={onClick}
      header={
        <GiveawaySummaryItem
          banner={token.icon || ''}
          className={expanded ? '!grid-cols-1 sm:!grid-cols-[170px_1fr]' : ''}
        >
          <div>
            <Tag
              title={title}
              className="!inline-flex !text-xs !font-semibold"
            />
          </div>
          <h2 className={`text-lg text-ch font-semibold break-all`}>
            Win up to {formatAmount(amount?.toString() || '', token.decimals)}{' '}
            {token.ticker}{' '}
          </h2>
          {token.tokenId ||
          (token.address && token.address !== ethers.constants.AddressZero) ? (
            <TokenAddress
              token={token}
              blockchain={blockchain}
              showBlockchain={true}
              className="mb-1"
            />
          ) : (
            <></>
          )}
          <TokenGiveawayClaimStats
            token={token}
            totalClaimable={claimableAmount}
            totalClaimed={claimedAmount}
            blockchain={blockchain}
          />
        </GiveawaySummaryItem>
      }
    >
      <div className="space-y-6 relative z-50">
        {children}
        {amount && claimedAmount.eq(amount) && (
          <SuccessAlertBox
            title="Congrats!"
            subtitle={
              <span>
                Your transaction was submitted successfully, sometimes it takes
                30-60 seconds for the explorer to index it.
              </span>
            }
          />
        )}
      </div>
    </Panel>
  );
}
