"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Giveaways_components_TokenGiveawayDetails_tsx";
exports.ids = ["components_Giveaways_components_TokenGiveawayDetails_tsx"];
exports.modules = {

/***/ "./components/AlertBox.tsx":
/*!*********************************!*\
  !*** ./components/AlertBox.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleWarningAlertBox: () => (/* binding */ SimpleWarningAlertBox),\n/* harmony export */   SuccessAlertBox: () => (/* binding */ SuccessAlertBox),\n/* harmony export */   WarningAlertBox: () => (/* binding */ WarningAlertBox),\n/* harmony export */   \"default\": () => (/* binding */ AlertBox)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nfunction AlertBox({ title, subtitle, icon, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`flex flex-col p-4 py-8 rounded-2xl relative overflow-hidden text-primary-foreground`, className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -top-[40px] -right-[26px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -bottom-[66px] -right-[60px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"primary-gradient-box-circle-icon\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg mb-0\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this),\n                            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-opacity-80\",\n                                children: subtitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\nconst SimpleWarningAlertBox = ({ title, description })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-red-100 border border-red-300 text-red-600 px-4 py-3 rounded relative mt-4 space-x-2\",\n        role: \"alert\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                className: \"font-bold\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 51,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"block sm:inline\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 52,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined);\nfunction SuccessAlertBox({ title, subtitle }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertBox, {\n        className: \"gradient-primary\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n            className: \"h-10 text-primary-foreground\",\n            size: 32\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n            lineNumber: 66,\n            columnNumber: 13\n        }, void 0),\n        title: title,\n        subtitle: subtitle\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\nfunction WarningAlertBox({ title, subtitle }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertBox, {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Info, {\n            fontSize: 28\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n            lineNumber: 82,\n            columnNumber: 13\n        }, void 0),\n        title: title,\n        subtitle: subtitle,\n        className: \"gradient-warning text-white\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/AlertBox.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/GiveawaySummaryItem.tsx":
/*!*****************************************************************!*\
  !*** ./components/Giveaways/components/GiveawaySummaryItem.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GiveawaySummaryItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction GiveawaySummaryItem({ banner, children, bannerTag, onClick, size = \"default\", actionText, className, actionSuccess }) {\n    const isVideo = banner?.endsWith(\".mp4\") || banner?.endsWith(\".webm\") || banner?.endsWith(\".mov\") || banner?.endsWith(\".gif\");\n    if (size === \"small\" || size == \"default\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `relative gap-4 grid grid-cols-[120px_1fr] items-center ${size === \"default\" ? \"sm:grid-cols-[170px_1fr]\" : \"\"} ${className || \"\"}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            height: 200,\n                            width: 200,\n                            src: banner,\n                            className: `object-cover aspect-square rounded-xl absolute top-0 left-0 w-full h-full blur-[100px]`,\n                            alt: \"giveaway-image\",\n                            unoptimized: isVideo\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 max-w-sm m-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    height: 200,\n                                    width: 200,\n                                    src: banner,\n                                    className: `object-cover w-full aspect-square rounded-xl`,\n                                    alt: \"giveaway-image\",\n                                    unoptimized: isVideo\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-1 left-0 w-full flex justify-center\",\n                                    children: bannerTag\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-20 gap-2 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: children\n                        }, void 0, false),\n                        actionText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm flex items-center gap-1 text-blue-500 font-medium cursor-pointer\",\n                            onClick: onClick,\n                            children: [\n                                actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                                    weight: \"bold\",\n                                    size: 15\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"line-clamp-1 break-all\",\n                                    children: actionText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    \"aria-hidden\": \"true\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative gap-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute transform-gpu -z-10 rounded-3xl w-full blur-3xl`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            height: 400,\n                            width: 400,\n                            src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                            className: `object-cover w-full aspect-square rounded-xl`,\n                            alt: \"giveaway-image\",\n                            unoptimized: isVideo\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pb-2 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                height: 400,\n                                width: 400,\n                                src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                                className: `object-cover aspect-square w-full rounded-xl`,\n                                alt: \"giveaway-image\",\n                                unoptimized: isVideo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2\",\n                                children: bannerTag\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-20 space-y-2\",\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"sm\",\n                            rounded: \"full\",\n                            className: \"!font-semibold mt-2\",\n                            block: true,\n                            onClick: onClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-1 items-center\",\n                                children: [\n                                    actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                                        weight: \"bold\",\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"line-clamp-1 break-all\",\n                                        children: actionText\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0dpdmVhd2F5cy9jb21wb25lbnRzL0dpdmVhd2F5U3VtbWFyeUl0ZW0udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStDO0FBQ0Q7QUFDZjtBQUVoQixTQUFTRyxvQkFBb0IsRUFDMUNDLE1BQU0sRUFDTkMsUUFBUSxFQUNSQyxTQUFTLEVBQ1RDLE9BQU8sRUFDUEMsT0FBTyxTQUFTLEVBQ2hCQyxVQUFVLEVBQ1ZDLFNBQVMsRUFDVEMsYUFBYSxFQVVkO0lBR0MsTUFBTUMsVUFDSlIsUUFBUVMsU0FBUyxXQUNqQlQsUUFBUVMsU0FBUyxZQUNqQlQsUUFBUVMsU0FBUyxXQUNqQlQsUUFBUVMsU0FBUztJQUVuQixJQUFJTCxTQUFTLFdBQVdBLFFBQVEsV0FBVztRQUN6QyxxQkFDRSw4REFBQ007WUFDQ0osV0FBVyxDQUFDLHVEQUF1RCxFQUNqRUYsU0FBUyxZQUFZLDZCQUE2QixHQUNuRCxDQUFDLEVBQUVFLGFBQWEsR0FBRyxDQUFDOzs4QkFFckIsOERBQUNJO29CQUFJSixXQUFVOztzQ0FDYiw4REFBQ1IsbURBQUtBOzRCQUNKYSxRQUFROzRCQUNSQyxPQUFPOzRCQUNQQyxLQUFLYjs0QkFDTE0sV0FBVyxDQUFDLHNGQUFzRixDQUFDOzRCQUNuR1EsS0FBSTs0QkFDSkMsYUFBYVA7Ozs7OztzQ0FHZiw4REFBQ0U7NEJBQUlKLFdBQVU7OzhDQUNiLDhEQUFDUixtREFBS0E7b0NBQ0phLFFBQVE7b0NBQ1JDLE9BQU87b0NBQ1BDLEtBQUtiO29DQUNMTSxXQUFXLENBQUMsNENBQTRDLENBQUM7b0NBQ3pEUSxLQUFJO29DQUNKQyxhQUFhUDs7Ozs7OzhDQUVmLDhEQUFDRTtvQ0FBSUosV0FBVTs4Q0FDWko7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFJUCw4REFBQ1E7b0JBQUlKLFdBQVU7O3NDQUNiO3NDQUFHTDs7d0JBQ0ZJLDRCQUNDLDhEQUFDSzs0QkFDQ0osV0FBVTs0QkFDVkgsU0FBU0E7O2dDQUVSSSwrQkFBaUIsOERBQUNWLHdEQUFLQTtvQ0FBQ21CLFFBQU87b0NBQU9aLE1BQU07Ozs7Ozs4Q0FDN0MsOERBQUNhO29DQUFLWCxXQUFVOzhDQUEwQkQ7Ozs7Ozs4Q0FDMUMsOERBQUNZO29DQUFLQyxlQUFZOzhDQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNckM7SUFFQSxxQkFDRSw4REFBQ1I7UUFBSUosV0FBVTs7MEJBQ2IsOERBQUNJO2dCQUFJSixXQUFVOztrQ0FDYiw4REFBQ0k7d0JBQ0NKLFdBQVcsQ0FBQyx3REFBd0QsQ0FBQztrQ0FFckUsNEVBQUNSLG1EQUFLQTs0QkFDSmEsUUFBUTs0QkFDUkMsT0FBTzs0QkFDUEMsS0FDRWIsVUFDQTs0QkFFRk0sV0FBVyxDQUFDLDRDQUE0QyxDQUFDOzRCQUN6RFEsS0FBSTs0QkFDSkMsYUFBYVA7Ozs7Ozs7Ozs7O2tDQUdqQiw4REFBQ0U7d0JBQUlKLFdBQVU7OzBDQUNiLDhEQUFDUixtREFBS0E7Z0NBQ0phLFFBQVE7Z0NBQ1JDLE9BQU87Z0NBQ1BDLEtBQ0ViLFVBQ0E7Z0NBRUZNLFdBQVcsQ0FBQyw0Q0FBNEMsQ0FBQztnQ0FDekRRLEtBQUk7Z0NBQ0pDLGFBQWFQOzs7Ozs7MENBRWYsOERBQUNFO2dDQUFJSixXQUFVOzBDQUEwQko7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFHN0MsOERBQUNRO2dCQUFJSixXQUFVOztvQkFDWkw7a0NBQ0QsOERBQUNTO2tDQUNDLDRFQUFDZCx5REFBTUE7NEJBQ0xRLE1BQUs7NEJBQ0xlLFNBQVE7NEJBQ1JiLFdBQVU7NEJBQ1ZjLEtBQUs7NEJBQ0xqQixTQUFTQTtzQ0FFVCw0RUFBQ087Z0NBQUlKLFdBQVU7O29DQUNaQywrQkFBaUIsOERBQUNWLHdEQUFLQTt3Q0FBQ21CLFFBQU87d0NBQU9aLE1BQU07Ozs7OztrREFDN0MsOERBQUNhO3dDQUFLWCxXQUFVO2tEQUEwQkQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPeEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWlybHlmdC9wdWJsaWMtdWkvLi9jb21wb25lbnRzL0dpdmVhd2F5cy9jb21wb25lbnRzL0dpdmVhd2F5U3VtbWFyeUl0ZW0udHN4P2I0ZDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQENvbXBvbmVudHMvdWkvYnV0dG9uJztcclxuaW1wb3J0IHsgQ2hlY2sgfSBmcm9tICdAcGhvc3Bob3ItaWNvbnMvcmVhY3QnO1xyXG5pbXBvcnQgSW1hZ2UgZnJvbSAnbmV4dC9pbWFnZSc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBHaXZlYXdheVN1bW1hcnlJdGVtKHtcclxuICBiYW5uZXIsXHJcbiAgY2hpbGRyZW4sXHJcbiAgYmFubmVyVGFnLFxyXG4gIG9uQ2xpY2ssXHJcbiAgc2l6ZSA9ICdkZWZhdWx0JyxcclxuICBhY3Rpb25UZXh0LFxyXG4gIGNsYXNzTmFtZSxcclxuICBhY3Rpb25TdWNjZXNzLFxyXG59OiB7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxuICBiYW5uZXI6IHN0cmluZztcclxuICBhY3Rpb25UZXh0Pzogc3RyaW5nO1xyXG4gIGJhbm5lclRhZz86IFJlYWN0LlJlYWN0Tm9kZTtcclxuICBvbkNsaWNrPzogKCkgPT4gdm9pZDtcclxuICBzaXplPzogJ3NtYWxsJyB8ICdkZWZhdWx0JyB8ICdsYXJnZSc7XHJcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xyXG4gIGFjdGlvblN1Y2Nlc3M/OiBib29sZWFuO1xyXG59KSB7XHJcblxyXG5cclxuICBjb25zdCBpc1ZpZGVvID1cclxuICAgIGJhbm5lcj8uZW5kc1dpdGgoJy5tcDQnKSB8fFxyXG4gICAgYmFubmVyPy5lbmRzV2l0aCgnLndlYm0nKSB8fFxyXG4gICAgYmFubmVyPy5lbmRzV2l0aCgnLm1vdicpIHx8XHJcbiAgICBiYW5uZXI/LmVuZHNXaXRoKCcuZ2lmJyk7XHJcblxyXG4gIGlmIChzaXplID09PSAnc21hbGwnIHx8IHNpemUgPT0gJ2RlZmF1bHQnKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2XHJcbiAgICAgICAgY2xhc3NOYW1lPXtgcmVsYXRpdmUgZ2FwLTQgZ3JpZCBncmlkLWNvbHMtWzEyMHB4XzFmcl0gaXRlbXMtY2VudGVyICR7XHJcbiAgICAgICAgICBzaXplID09PSAnZGVmYXVsdCcgPyAnc206Z3JpZC1jb2xzLVsxNzBweF8xZnJdJyA6ICcnXHJcbiAgICAgICAgfSAke2NsYXNzTmFtZSB8fCAnJ31gfVxyXG4gICAgICA+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxyXG4gICAgICAgICAgPEltYWdlXHJcbiAgICAgICAgICAgIGhlaWdodD17MjAwfVxyXG4gICAgICAgICAgICB3aWR0aD17MjAwfVxyXG4gICAgICAgICAgICBzcmM9e2Jhbm5lcn1cclxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgb2JqZWN0LWNvdmVyIGFzcGVjdC1zcXVhcmUgcm91bmRlZC14bCBhYnNvbHV0ZSB0b3AtMCBsZWZ0LTAgdy1mdWxsIGgtZnVsbCBibHVyLVsxMDBweF1gfVxyXG4gICAgICAgICAgICBhbHQ9XCJnaXZlYXdheS1pbWFnZVwiXHJcbiAgICAgICAgICAgIHVub3B0aW1pemVkPXtpc1ZpZGVvfVxyXG4gICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTAgbWF4LXctc20gbS1hdXRvXCI+XHJcbiAgICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICAgIGhlaWdodD17MjAwfVxyXG4gICAgICAgICAgICAgIHdpZHRoPXsyMDB9XHJcbiAgICAgICAgICAgICAgc3JjPXtiYW5uZXJ9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgb2JqZWN0LWNvdmVyIHctZnVsbCBhc3BlY3Qtc3F1YXJlIHJvdW5kZWQteGxgfVxyXG4gICAgICAgICAgICAgIGFsdD1cImdpdmVhd2F5LWltYWdlXCJcclxuICAgICAgICAgICAgICB1bm9wdGltaXplZD17aXNWaWRlb31cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tMSBsZWZ0LTAgdy1mdWxsIGZsZXgganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICB7YmFubmVyVGFnfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0yMCBnYXAtMiBmbGV4IGZsZXgtY29sXCI+XHJcbiAgICAgICAgICA8PntjaGlsZHJlbn08Lz5cclxuICAgICAgICAgIHthY3Rpb25UZXh0ICYmIChcclxuICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTEgdGV4dC1ibHVlLTUwMCBmb250LW1lZGl1bSBjdXJzb3ItcG9pbnRlclwiXHJcbiAgICAgICAgICAgICAgb25DbGljaz17b25DbGlja31cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIHthY3Rpb25TdWNjZXNzICYmIDxDaGVjayB3ZWlnaHQ9XCJib2xkXCIgc2l6ZT17MTV9IC8+fVxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImxpbmUtY2xhbXAtMSBicmVhay1hbGxcIj57YWN0aW9uVGV4dH08L3NwYW4+XHJcbiAgICAgICAgICAgICAgPHNwYW4gYXJpYS1oaWRkZW49XCJ0cnVlXCI+4oaSPC9zcGFuPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGdhcC0zXCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cclxuICAgICAgICA8ZGl2XHJcbiAgICAgICAgICBjbGFzc05hbWU9e2BhYnNvbHV0ZSB0cmFuc2Zvcm0tZ3B1IC16LTEwIHJvdW5kZWQtM3hsIHctZnVsbCBibHVyLTN4bGB9XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPEltYWdlXHJcbiAgICAgICAgICAgIGhlaWdodD17NDAwfVxyXG4gICAgICAgICAgICB3aWR0aD17NDAwfVxyXG4gICAgICAgICAgICBzcmM9e1xyXG4gICAgICAgICAgICAgIGJhbm5lciB8fFxyXG4gICAgICAgICAgICAgICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTY1NTcyMDQwODg2MS04YjA0YzA3MjRmZDk/aXhsaWI9cmItNC4wLjMmaXhpZD1Nbnd4TWpBM2ZEQjhNSHh3YUc5MGJ5MXdZV2RsZkh4OGZHVnVmREI4Zkh4OCZhdXRvPWZvcm1hdCZmaXQ9Y3JvcCZ3PTE5MzImcT04MCdcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9e2BvYmplY3QtY292ZXIgdy1mdWxsIGFzcGVjdC1zcXVhcmUgcm91bmRlZC14bGB9XHJcbiAgICAgICAgICAgIGFsdD1cImdpdmVhd2F5LWltYWdlXCJcclxuICAgICAgICAgICAgdW5vcHRpbWl6ZWQ9e2lzVmlkZW99XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicGItMiByZWxhdGl2ZSB6LTEwXCI+XHJcbiAgICAgICAgICA8SW1hZ2VcclxuICAgICAgICAgICAgaGVpZ2h0PXs0MDB9XHJcbiAgICAgICAgICAgIHdpZHRoPXs0MDB9XHJcbiAgICAgICAgICAgIHNyYz17XHJcbiAgICAgICAgICAgICAgYmFubmVyIHx8XHJcbiAgICAgICAgICAgICAgJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNjU1NzIwNDA4ODYxLThiMDRjMDcyNGZkOT9peGxpYj1yYi00LjAuMyZpeGlkPU1ud3hNakEzZkRCOE1IeHdhRzkwYnkxd1lXZGxmSHg4ZkdWdWZEQjhmSHg4JmF1dG89Zm9ybWF0JmZpdD1jcm9wJnc9MTkzMiZxPTgwJ1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT17YG9iamVjdC1jb3ZlciBhc3BlY3Qtc3F1YXJlIHctZnVsbCByb3VuZGVkLXhsYH1cclxuICAgICAgICAgICAgYWx0PVwiZ2l2ZWF3YXktaW1hZ2VcIlxyXG4gICAgICAgICAgICB1bm9wdGltaXplZD17aXNWaWRlb31cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0yIHJpZ2h0LTJcIj57YmFubmVyVGFnfTwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTIwIHNwYWNlLXktMlwiPlxyXG4gICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICByb3VuZGVkPVwiZnVsbFwiXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIiFmb250LXNlbWlib2xkIG10LTJcIlxyXG4gICAgICAgICAgICBibG9ja1xyXG4gICAgICAgICAgICBvbkNsaWNrPXtvbkNsaWNrfVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTEgaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAge2FjdGlvblN1Y2Nlc3MgJiYgPENoZWNrIHdlaWdodD1cImJvbGRcIiBzaXplPXsxNn0gLz59XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibGluZS1jbGFtcC0xIGJyZWFrLWFsbFwiPnthY3Rpb25UZXh0fTwvc3Bhbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJCdXR0b24iLCJDaGVjayIsIkltYWdlIiwiR2l2ZWF3YXlTdW1tYXJ5SXRlbSIsImJhbm5lciIsImNoaWxkcmVuIiwiYmFubmVyVGFnIiwib25DbGljayIsInNpemUiLCJhY3Rpb25UZXh0IiwiY2xhc3NOYW1lIiwiYWN0aW9uU3VjY2VzcyIsImlzVmlkZW8iLCJlbmRzV2l0aCIsImRpdiIsImhlaWdodCIsIndpZHRoIiwic3JjIiwiYWx0IiwidW5vcHRpbWl6ZWQiLCJ3ZWlnaHQiLCJzcGFuIiwiYXJpYS1oaWRkZW4iLCJyb3VuZGVkIiwiYmxvY2siXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Giveaways/components/GiveawaySummaryItem.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/TokenAddress.tsx":
/*!**********************************************************!*\
  !*** ./components/Giveaways/components/TokenAddress.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenAddress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction TokenAddress({ token, blockchain, className, showBlockchain = false, addressChars = 3 }) {\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    if (token.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.AssetType.NATIVE) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex gap-1.5 text-sm text-cl items-center ${className || \"\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium\",\n                children: blockchain?.type === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.BlockchainType.DOTSAMA ? token.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.AssetType.DOTSAMA_NFT ? `NFT #${token.tokenId}` : `Token #${token.tokenId}` : (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.shortenAddress)(token.address, addressChars)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                className: \"text-primary-foreground bg-primary rounded-full p-1\",\n                weight: \"bold\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Copy, {\n                className: \"cursor-pointer text-ch\",\n                size: 16,\n                onClick: (event)=>{\n                    event.stopPropagation();\n                    const textToCopy = blockchain?.type === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.BlockchainType.DOTSAMA ? token.tokenId : token.address;\n                    navigator.clipboard.writeText(textToCopy);\n                    setCopied(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this),\n            blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"inline-flex text-ch\",\n                target: \"_blank\",\n                href: blockchain.type === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.BlockchainType.DOTSAMA ? (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getExplorerLink)(blockchain.blockExplorerUrls, token.tokenId, token.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.AssetType.DOTSAMA_NFT ? _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ExplorerDataType.DOTSAMA_NFT : _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ExplorerDataType.DOTSAMA_TOKEN) : (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getExplorerLink)(blockchain.blockExplorerUrls, token.address, _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ExplorerDataType.TOKEN),\n                rel: \"noreferrer\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.ArrowSquareOut, {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this),\n            showBlockchain && blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_5___default()), {\n                src: blockchain?.icon || \"\",\n                height: 20,\n                width: 20,\n                className: \"h-5 w-5\",\n                alt: \"blockchain-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenAddress.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/TokenGiveawayClaimStats.tsx":
/*!*********************************************************************!*\
  !*** ./components/Giveaways/components/TokenGiveawayClaimStats.tsx ***!
  \*********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenGiveawayClaimStats: () => (/* binding */ TokenGiveawayClaimStats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction TokenGiveawayClaimStats({ totalClaimable, totalClaimed, token, blockchain }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2 text-sm text-ch font-medium \",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Coins, {\n                        size: 20,\n                        className: \"flex-shrink-0\",\n                        weight: \"duotone\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"break-all\",\n                        children: [\n                            (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatAmount)(totalClaimed?.toString(), token.decimals),\n                            \" \",\n                            token.ticker,\n                            \" Claimed\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Coins, {\n                        size: 20,\n                        className: \"flex-shrink-0\",\n                        weight: \"duotone\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"break-all\",\n                        children: [\n                            (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatAmount)(totalClaimable?.toString(), token.decimals),\n                            \" \",\n                            token.ticker,\n                            \" Claimable\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenGiveawayClaimStats.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/TokenGiveawayDetails.tsx":
/*!******************************************************************!*\
  !*** ./components/Giveaways/components/TokenGiveawayDetails.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenGiveawayDetails)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Panel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Panel */ \"./components/Panel.tsx\");\n/* harmony import */ var _Components_Tag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Tag */ \"./components/Tag.tsx\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ethers__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_TokenGiveawayClaimStats__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/TokenGiveawayClaimStats */ \"./components/Giveaways/components/TokenGiveawayClaimStats.tsx\");\n/* harmony import */ var _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./GiveawaySummaryItem */ \"./components/Giveaways/components/GiveawaySummaryItem.tsx\");\n/* harmony import */ var _TokenAddress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TokenAddress */ \"./components/Giveaways/components/TokenAddress.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_2__, _Components_Panel__WEBPACK_IMPORTED_MODULE_3__, _Components_Tag__WEBPACK_IMPORTED_MODULE_4__, _components_TokenGiveawayClaimStats__WEBPACK_IMPORTED_MODULE_6__, _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_7__, _TokenAddress__WEBPACK_IMPORTED_MODULE_8__]);\n([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_2__, _Components_Panel__WEBPACK_IMPORTED_MODULE_3__, _Components_Tag__WEBPACK_IMPORTED_MODULE_4__, _components_TokenGiveawayClaimStats__WEBPACK_IMPORTED_MODULE_6__, _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_7__, _TokenAddress__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nfunction TokenGiveawayDetails({ token, blockchain, amount, claimableAmount, claimedAmount, expandable, expanded, children, onClick, title }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Panel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        expandable: expandable,\n        expanded: expanded,\n        onClick: onClick,\n        header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            banner: token.icon || \"\",\n            className: expanded ? \"!grid-cols-1 sm:!grid-cols-[170px_1fr]\" : \"\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_4__.Tag, {\n                        title: title,\n                        className: \"!inline-flex !text-xs !font-semibold\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 11\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: `text-lg text-ch font-semibold break-all`,\n                    children: [\n                        \"Win up to \",\n                        (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatAmount)(amount?.toString() || \"\", token.decimals),\n                        \" \",\n                        token.ticker,\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 11\n                }, void 0),\n                token.tokenId || token.address && token.address !== ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.constants.AddressZero ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAddress__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    token: token,\n                    blockchain: blockchain,\n                    showBlockchain: true,\n                    className: \"mb-1\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 13\n                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TokenGiveawayClaimStats__WEBPACK_IMPORTED_MODULE_6__.TokenGiveawayClaimStats, {\n                    token: token,\n                    totalClaimable: claimableAmount,\n                    totalClaimed: claimedAmount,\n                    blockchain: blockchain\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n            lineNumber: 41,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 relative z-50\",\n            children: [\n                children,\n                amount && claimedAmount.eq(amount) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_2__.SuccessAlertBox, {\n                    title: \"Congrats!\",\n                    subtitle: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Your transaction was submitted successfully, sometimes it takes 30-60 seconds for the explorer to index it.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenGiveawayDetails.tsx\n");

/***/ }),

/***/ "./components/Panel.tsx":
/*!******************************!*\
  !*** ./components/Panel.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Panel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__]);\n_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nfunction Panel({ expandable, expanded, header, children, onClick }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`rounded-xl p-4 transition`, expandable ? expanded ? \"cursor-pointer\" : \"hover:bg-foreground/10 cursor-pointer\" : \"\"),\n                onClick: ()=>{\n                    expandable && onClick?.();\n                },\n                children: header\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Panel.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`p-4`, expandable && !expanded ? \"hidden\" : \"\"),\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Panel.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1BhbmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF1QztBQUV4QixTQUFTQyxNQUFNLEVBQzVCQyxVQUFVLEVBQ1ZDLFFBQVEsRUFDUkMsTUFBTSxFQUNOQyxRQUFRLEVBQ1JDLE9BQU8sRUFPUjtJQUNDLHFCQUNFOzswQkFDRSw4REFBQ0M7Z0JBQ0NDLFdBQVdSLHFEQUFFQSxDQUNYLENBQUMseUJBQXlCLENBQUMsRUFDM0JFLGFBQ0lDLFdBQ0UsbUJBQ0EsMENBQ0Y7Z0JBRU5HLFNBQVM7b0JBQ1BKLGNBQWNJO2dCQUNoQjswQkFFQ0Y7Ozs7OzswQkFFSCw4REFBQ0c7Z0JBQUlDLFdBQVdSLHFEQUFFQSxDQUFDLENBQUMsR0FBRyxDQUFDLEVBQUVFLGNBQWMsQ0FBQ0MsV0FBVyxXQUFXOzBCQUM1REU7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWlybHlmdC9wdWJsaWMtdWkvLi9jb21wb25lbnRzL1BhbmVsLnRzeD9lMDM1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNuIH0gZnJvbSAnQFJvb3QvdXRpbHMvdXRpbHMnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFuZWwoe1xyXG4gIGV4cGFuZGFibGUsXHJcbiAgZXhwYW5kZWQsXHJcbiAgaGVhZGVyLFxyXG4gIGNoaWxkcmVuLFxyXG4gIG9uQ2xpY2ssXHJcbn06IHtcclxuICBleHBhbmRhYmxlPzogYm9vbGVhbjtcclxuICBleHBhbmRlZD86IGJvb2xlYW47XHJcbiAgaGVhZGVyOiBSZWFjdC5SZWFjdE5vZGU7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxuICBvbkNsaWNrPzogKCkgPT4gdm9pZDtcclxufSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8ZGl2XHJcbiAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgIGByb3VuZGVkLXhsIHAtNCB0cmFuc2l0aW9uYCxcclxuICAgICAgICAgIGV4cGFuZGFibGVcclxuICAgICAgICAgICAgPyBleHBhbmRlZFxyXG4gICAgICAgICAgICAgID8gJ2N1cnNvci1wb2ludGVyJ1xyXG4gICAgICAgICAgICAgIDogJ2hvdmVyOmJnLWZvcmVncm91bmQvMTAgY3Vyc29yLXBvaW50ZXInXHJcbiAgICAgICAgICAgIDogJycsXHJcbiAgICAgICAgKX1cclxuICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICBleHBhbmRhYmxlICYmIG9uQ2xpY2s/LigpO1xyXG4gICAgICAgIH19XHJcbiAgICAgID5cclxuICAgICAgICB7aGVhZGVyfVxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9e2NuKGBwLTRgLCBleHBhbmRhYmxlICYmICFleHBhbmRlZCA/ICdoaWRkZW4nIDogJycpfT5cclxuICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC8+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiY24iLCJQYW5lbCIsImV4cGFuZGFibGUiLCJleHBhbmRlZCIsImhlYWRlciIsImNoaWxkcmVuIiwib25DbGljayIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/Panel.tsx\n");

/***/ })

};
;